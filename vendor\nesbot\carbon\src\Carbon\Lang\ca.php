<?php

/*
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array(
    'year' => ':count any|:count anys',
    'y' => ':count any|:count anys',
    'month' => ':count mes|:count mesos',
    'm' => ':count mes|:count mesos',
    'week' => ':count setmana|:count setmanes',
    'w' => ':count setmana|:count setmanes',
    'day' => ':count dia|:count dies',
    'd' => ':count dia|:count dies',
    'hour' => ':count hora|:count hores',
    'h' => ':count hora|:count hores',
    'minute' => ':count minut|:count minuts',
    'min' => ':count minut|:count minuts',
    'second' => ':count segon|:count segons',
    's' => ':count segon|:count segons',
    'ago' => 'fa :time',
    'from_now' => 'd\'aquí :time',
    'after' => ':time després',
    'before' => ':time abans',
    'diff_now' => 'ara mateix',
    'diff_yesterday' => 'ahir',
    'diff_tomorrow' => 'demà',
    'diff_before_yesterday' => "abans d'ahir",
    'diff_after_tomorrow' => 'demà passat',
    'period_recurrences' => ':count cop|:count cops',
    'period_interval' => 'cada :interval',
    'period_start_date' => 'de :date',
    'period_end_date' => 'fins a :date',
);
