{"name": "phpoffice/phpexcel", "description": "PHPExcel - OpenXML - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "keywords": ["PHP", "Excel", "OpenXML", "xlsx", "xls", "spreadsheet"], "homepage": "http://phpexcel.codeplex.com", "type": "library", "license": "LGPL", "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.maartenballiauw.be"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.rootslabs.net"}, {"name": "<PERSON>"}], "require": {"php": ">=5.2.0", "ext-xml": "*", "ext-xmlwriter": "*"}, "recommend": {"ext-zip": "*", "ext-gd2": "*"}, "autoload": {"psr-0": {"PHPExcel": "Classes/"}}}