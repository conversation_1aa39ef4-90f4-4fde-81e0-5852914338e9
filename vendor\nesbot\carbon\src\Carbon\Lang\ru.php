<?php

/*
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array(
    'year' => ':count год|:count года|:count лет',
    'y' => ':count г|:count г|:count л',
    'month' => ':count месяц|:count месяца|:count месяцев',
    'm' => ':count м|:count м|:count м',
    'week' => ':count неделю|:count недели|:count недель',
    'w' => ':count н|:count н|:count н',
    'day' => ':count день|:count дня|:count дней',
    'd' => ':count д|:count д|:count д',
    'hour' => ':count час|:count часа|:count часов',
    'h' => ':count ч|:count ч|:count ч',
    'minute' => ':count минуту|:count минуты|:count минут',
    'min' => ':count мин|:count мин|:count мин',
    'second' => ':count секунду|:count секунды|:count секунд',
    's' => ':count с|:count с|:count с',
    'ago' => ':time назад',
    'from_now' => 'через :time',
    'after' => ':time после',
    'before' => ':time до',
);
