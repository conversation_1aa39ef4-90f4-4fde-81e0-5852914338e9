# Available column formatting

| Format name  | PHPExcel class reference|
| ------------- |-----------------|
|General|PHPExcel_Style_NumberFormat::FORMAT_GENERAL
|@|PHPExcel_Style_NumberFormat::FORMAT_TEXT
|0|PHPExcel_Style_NumberFormat::FORMAT_NUMBER
|0.00|PHPExcel_Style_NumberFormat::FORMAT_NUMBER_00
|#,##0.00|PHPExcel_Style_NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1
|#,##0.00_-|PHPExcel_Style_NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2
|0%|PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE
|0.00%|PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE_00
|yyyy-mm-dd|PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD2
|yy-mm-dd|PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD
|dd/mm/yy|PHPExcel_Style_NumberFormat::FORMAT_DATE_DDMMYYYY
|d/m/y|PHPExcel_Style_NumberFormat::FORMAT_DATE_DMYSLASH
|d-m-y|PHPExcel_Style_NumberFormat::FORMAT_DATE_DMYMINUS
|d-m|PHPExcel_Style_NumberFormat::FORMAT_DATE_DMMINUS
|m-y|PHPExcel_Style_NumberFormat::FORMAT_DATE_MYMINUS
|mm-dd-yy|PHPExcel_Style_NumberFormat::FORMAT_DATE_XLSX14
|d-mmm-yy|PHPExcel_Style_NumberFormat::FORMAT_DATE_XLSX15
|d-mmm|PHPExcel_Style_NumberFormat::FORMAT_DATE_XLSX16
|mmm-yy|PHPExcel_Style_NumberFormat::FORMAT_DATE_XLSX17
|m/d/yy h:mm|PHPExcel_Style_NumberFormat::FORMAT_DATE_XLSX22
|d/m/y h:mm|PHPExcel_Style_NumberFormat::FORMAT_DATE_DATETIME
|h:mm AM/PM|PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME1
|h:mm:ss AM/PM|PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME2
|h:mm|PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME3
|h:mm:ss|PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME4
|mm:ss|PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME5
|h:mm:ss|PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME6
|i:s.S|PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME7
|h:mm:ss;@|PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME8
|yy/mm/dd;@|PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDDSLASH
|"$"#,##0.00_-|PHPExcel_Style_NumberFormat::FORMAT_CURRENCY_USD_SIMPLE
|$#,##0_-|PHPExcel_Style_NumberFormat::FORMAT_CURRENCY_USD
|[$EUR ]#,##0.00_-|PHPExcel_Style_NumberFormat::FORMAT_CURRENCY_EUR_SIMPLE