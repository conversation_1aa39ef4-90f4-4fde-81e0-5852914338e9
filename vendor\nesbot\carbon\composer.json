{"name": "nesbot/carbon", "type": "library", "description": "A simple API extension for DateTime.", "keywords": ["date", "time", "DateTime"], "homepage": "http://carbon.nesbot.com", "support": {"issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}], "bin": ["bin/upgrade-carbon"], "require": {"php": ">=5.3.9", "kylekatarnls/update-helper": "^1.1", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"composer/composer": "^1.2", "friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "autoload": {"psr-4": {"": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "config": {"sort-packages": true}, "scripts": {"test": ["@phpunit", "@phpcs"], "phpunit": "phpunit --verbose --coverage-clover=coverage.xml", "phpcs": "php-cs-fixer fix -v --diff --dry-run", "phpstan": "phpstan analyse --configuration phpstan.neon --level 3 src tests", "post-autoload-dump": ["UpdateHelper\\UpdateHelper::check"], "upgrade-carbon": ["Carbon\\Upgrade::upgrade"]}, "extra": {"update-helper": "Carbon\\Upgrade", "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}}}