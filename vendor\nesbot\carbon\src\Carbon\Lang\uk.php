<?php

/*
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array(
    'year' => ':count рік|:count роки|:count років',
    'y' => ':count рік|:count роки|:count років',
    'month' => ':count місяць|:count місяці|:count місяців',
    'm' => ':count місяць|:count місяці|:count місяців',
    'week' => ':count тиждень|:count тижні|:count тижнів',
    'w' => ':count тиждень|:count тижні|:count тижнів',
    'day' => ':count день|:count дні|:count днів',
    'd' => ':count день|:count дні|:count днів',
    'hour' => ':count година|:count години|:count годин',
    'h' => ':count година|:count години|:count годин',
    'minute' => ':count хвилину|:count хвилини|:count хвилин',
    'min' => ':count хвилину|:count хвилини|:count хвилин',
    'second' => ':count секунду|:count секунди|:count секунд',
    's' => ':count секунду|:count секунди|:count секунд',
    'ago' => ':time тому',
    'from_now' => 'через :time',
    'after' => ':time після',
    'before' => ':time до',
    'diff_now' => 'щойно',
    'diff_yesterday' => 'вчора',
    'diff_tomorrow' => 'завтра',
    'diff_before_yesterday' => 'позавчора',
    'diff_after_tomorrow' => 'післязавтра',
    'period_recurrences' => 'один раз|:count рази|:count разів',
    'period_interval' => 'кожні :interval',
    'period_start_date' => 'з :date',
    'period_end_date' => 'до :date',
);
