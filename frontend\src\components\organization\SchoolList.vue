<template>
  <div class="school-list">
    <div class="list-header">
      <h3>学校列表</h3>
      <div class="header-actions">
        <el-button type="primary" :icon="Plus" @click="showAddDialog">
          新增学校
        </el-button>
        <el-button :icon="Refresh" @click="loadSchools">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="filters" class="filter-form">
        <el-form-item label="学校名称">
          <el-input 
            v-model="filters.name" 
            placeholder="请输入学校名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="学校代码">
          <el-input 
            v-model="filters.code" 
            placeholder="请输入学校代码"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="学校类型">
          <el-select 
            v-model="filters.school_type" 
            placeholder="请选择学校类型"
            clearable
            style="width: 150px"
          >
            <el-option label="小学" value="primary" />
            <el-option label="初中" value="junior_high" />
            <el-option label="高中" value="senior_high" />
            <el-option label="九年一贯制" value="nine_year" />
            <el-option label="完全中学" value="complete_middle" />
            <el-option label="职业学校" value="vocational" />
            <el-option label="特殊教育学校" value="special_education" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属组织">
          <el-tree-select
            v-model="filters.parent_id"
            :data="organizationTree"
            :props="treeProps"
            placeholder="请选择所属组织"
            check-strictly
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchSchools" :loading="loading">
            搜索
          </el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 学校表格 -->
    <el-card class="table-card">
      <el-table 
        :data="schools" 
        style="width: 100%"
        v-loading="loading"
        row-key="id"
      >
        <el-table-column prop="name" label="学校名称" width="180" />
        <el-table-column prop="code" label="学校代码" width="120" />
        <el-table-column prop="school_type" label="学校类型" width="120">
          <template #default="{ row }">
            {{ getSchoolTypeText(row.school_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="education_level" label="教育层次" width="120">
          <template #default="{ row }">
            {{ getEducationLevelText(row.education_level) }}
          </template>
        </el-table-column>
        <el-table-column prop="parent.name" label="所属组织" width="150" />
        <el-table-column prop="principal_name" label="校长" width="100" />
        <el-table-column prop="principal_phone" label="校长电话" width="120" />
        <el-table-column prop="contact_person" label="联系人" width="100" />
        <el-table-column prop="contact_phone" label="联系电话" width="120" />
        <el-table-column prop="student_count" label="学生数" width="80" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewSchool(row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="editSchool(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteSchool(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadSchools"
          @current-change="loadSchools"
        />
      </div>
    </el-card>

    <!-- 学校详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="学校详情"
      width="80%"
      :before-close="handleDetailDialogClose"
    >
      <div v-if="selectedSchool" class="school-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="学校名称">
            {{ selectedSchool.name }}
          </el-descriptions-item>
          <el-descriptions-item label="学校代码">
            {{ selectedSchool.code }}
          </el-descriptions-item>
          <el-descriptions-item label="学校类型">
            {{ getSchoolTypeText(selectedSchool.school_type) }}
          </el-descriptions-item>
          <el-descriptions-item label="教育层次">
            {{ getEducationLevelText(selectedSchool.education_level) }}
          </el-descriptions-item>
          <el-descriptions-item label="所属组织">
            {{ selectedSchool.parent?.name || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="校长姓名">
            {{ selectedSchool.principal_name }}
          </el-descriptions-item>
          <el-descriptions-item label="校长电话">
            {{ selectedSchool.principal_phone }}
          </el-descriptions-item>
          <el-descriptions-item label="校长邮箱">
            {{ selectedSchool.principal_email || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系人">
            {{ selectedSchool.contact_person }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ selectedSchool.contact_phone }}
          </el-descriptions-item>
          <el-descriptions-item label="学校地址" :span="2">
            {{ selectedSchool.address }}
          </el-descriptions-item>
          <el-descriptions-item label="学生人数">
            {{ selectedSchool.student_count || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="校园面积">
            {{ selectedSchool.campus_area ? selectedSchool.campus_area + '平方米' : '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="建校年份">
            {{ selectedSchool.founded_year || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedSchool.status === 'active' ? 'success' : 'danger'">
              {{ selectedSchool.status === 'active' ? '正常' : '停用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(selectedSchool.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(selectedSchool.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 额外信息 -->
        <div v-if="selectedSchool.extra_data" class="extra-info">
          <h4>额外信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="班级数">
              {{ selectedSchool.extra_data.class_count || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="教师人数">
              {{ selectedSchool.extra_data.teacher_count || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="设施设备" :span="2">
              {{ selectedSchool.extra_data.facilities || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="特色项目" :span="2">
              {{ selectedSchool.extra_data.special_programs || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import { organizationApi } from '../../api/organization'

// 响应式数据
const loading = ref(false)
const schools = ref([])
const organizationTree = ref([])

const pagination = reactive({
  current_page: 1,
  per_page: 20,
  total: 0
})

const filters = reactive({
  name: '',
  code: '',
  school_type: '',
  parent_id: null
})

const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// 详情对话框
const detailDialogVisible = ref(false)
const selectedSchool = ref(null)

// 学校类型映射
const schoolTypeMap = {
  'primary': '小学',
  'junior_high': '初中',
  'senior_high': '高中',
  'nine_year': '九年一贯制',
  'complete_middle': '完全中学',
  'vocational': '职业学校',
  'special_education': '特殊教育学校'
}

// 教育层次映射
const educationLevelMap = {
  'preschool': '学前教育',
  'primary': '小学教育',
  'junior_high': '初中教育',
  'senior_high': '高中教育',
  'vocational': '中等职业教育',
  'special': '特殊教育'
}

// 方法
const loadOrganizationTree = async () => {
  try {
    const response = await organizationApi.getTree()
    organizationTree.value = response.data.data
  } catch (error) {
    console.error('加载组织树失败:', error)
  }
}

const loadSchools = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current_page,
      per_page: pagination.per_page,
      type: 'school', // 只查询学校类型的组织
      ...filters
    }
    
    const response = await organizationApi.getList(params)
    const data = response.data.data
    
    schools.value = data.data
    pagination.total = data.total
    pagination.current_page = data.current_page
    pagination.per_page = data.per_page
  } catch (error) {
    console.error('加载学校列表失败:', error)
    ElMessage.error('加载学校列表失败')
  } finally {
    loading.value = false
  }
}

const searchSchools = () => {
  pagination.current_page = 1
  loadSchools()
}

const resetFilters = () => {
  filters.name = ''
  filters.code = ''
  filters.school_type = ''
  filters.parent_id = null
  searchSchools()
}

const showAddDialog = () => {
  ElMessage.info('新增学校功能开发中...')
}

const viewSchool = async (school) => {
  try {
    const response = await organizationApi.getDetail(school.id)
    selectedSchool.value = response.data.data
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取学校详情失败:', error)
    ElMessage.error('获取学校详情失败')
  }
}

const editSchool = (school) => {
  ElMessage.info('编辑学校功能开发中...')
}

const deleteSchool = async (school) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除学校"${school.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await organizationApi.delete(school.id)
    ElMessage.success('删除成功')
    loadSchools()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除学校失败:', error)
      ElMessage.error('删除学校失败')
    }
  }
}

const handleDetailDialogClose = () => {
  detailDialogVisible.value = false
  selectedSchool.value = null
}

const getSchoolTypeText = (type) => {
  return schoolTypeMap[type] || type
}

const getEducationLevelText = (level) => {
  return educationLevelMap[level] || level
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '无'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadOrganizationTree()
  loadSchools()
})
</script>

<style scoped>
.school-list {
  padding: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.list-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-card,
.table-card {
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 学校详情样式 */
.school-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.extra-info {
  margin-top: 20px;
}

.extra-info h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .school-list {
    padding: 10px;
  }

  .list-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-form {
    display: block;
  }

  .filter-form .el-form-item {
    margin-bottom: 12px;
    display: block;
  }

  .filter-form .el-form-item:last-child {
    margin-bottom: 0;
  }
}

/* 表格样式调整 */
:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table .cell) {
  padding: 8px 12px;
}

/* 描述列表样式 */
:deep(.el-descriptions) {
  margin-bottom: 16px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
