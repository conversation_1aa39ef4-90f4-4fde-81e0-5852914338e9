# 实验教学管理系统 - 已实现功能开发文档

## 项目基本信息

**项目名称**: 实验教学管理系统 (GCQETS - General Chemistry Quality Education Teaching System)  
**项目目标**: 构建一个支持五级权限管理的实验教学综合管理平台  
**开发模式**: AI辅助开发  
**文档版本**: v2.0  
**最后更新**: 2025-06-21  
**项目路径**: F:\xampp\htdocs\gcqets  
**数据库**: gcqets (账号: root, 密码: liningyu2000)

## 技术架构

### 前端技术栈
- **Vue 3.3.4** + Composition API
- **Vue Router 4.2.4** (路由管理)
- **Pinia 2.1.6** (状态管理)
- **Element Plus 2.9.5** (UI组件库)
- **Axios 1.4.0** (HTTP客户端)
- **Vite 4.5.9** (构建工具)
- **ECharts** (图表组件)

### 后端技术栈
- **PHP 8.2.12**
- **Laravel 12.0.1** (Web框架)
- **MySQL/MariaDB 10.4.32** (数据库)
- **Laravel Sanctum** (API认证)
- **Composer 2.6.10** (依赖管理)

### 项目结构
```
F:\xampp\htdocs\gcqets
├── frontend/          # Vue3前端项目
│   ├── src/
│   │   ├── api/       # API服务层
│   │   ├── components/# 公共组件
│   │   ├── stores/    # Pinia状态管理
│   │   ├── views/     # 页面组件
│   │   ├── router/    # 路由配置
│   │   └── utils/     # 工具函数
│   └── package.json
└── backend/           # Laravel后端项目
    ├── app/
    │   ├── Http/Controllers/Api/  # API控制器
    │   ├── Models/               # 数据模型
    │   ├── Http/Middleware/      # 中间件
    │   └── Http/Requests/        # 请求验证
    ├── database/
    │   ├── migrations/           # 数据库迁移
    │   └── seeders/             # 数据填充
    └── routes/api.php           # API路由
```

## 已实现的核心功能

### 1. 用户认证系统 ✅

#### 后端实现
- **AuthController**: 完整的登录/登出/用户信息获取
- **Laravel Sanctum**: API Token认证
- **中间件**: 权限检查和认证验证

#### 前端实现
- **登录页面**: `/frontend/src/views/auth/Login.vue`
- **认证状态管理**: `/frontend/src/stores/auth.js`
- **路由守卫**: 自动重定向未认证用户
- **Token管理**: 自动添加Authorization头

#### 测试账户
```
系统管理员: sysadmin / 123456
学区管理员: lianzhou_admin / 123456  
校长用户: dongcheng_principal / 123456
```

### 2. 五级权限管理系统 ✅

#### 权限层级结构
```
第1级: 省级 (Province) - 管理全省所有下级单位
第2级: 市级 (City) - 管理本市所有下级单位  
第3级: 区县级 (District) - 管理本区县所有下级单位
第4级: 学区级 (Education Zone) - 管理本学区所有学校
第5级: 学校级 (School) - 仅管理本校事务
```

#### 数据库设计
- **organizations表**: 组织机构层级管理
- **users表**: 用户基础信息 + 扩展字段
- **roles表**: 角色定义和权限级别
- **permissions表**: 权限树形结构
- **关联表**: role_user, permission_role, user_organizations, user_permissions

#### 权限控制实现
- **后端**: CheckPermission中间件，基于角色和组织的权限验证
- **前端**: 权限指令 v-permission, v-role
- **数据访问**: 基于用户权限级别的数据范围控制

### 3. 组织机构管理 ✅

#### 功能特性
- **树形结构**: 支持五级组织架构展示
- **CRUD操作**: 创建、查看、编辑、删除组织
- **权限控制**: 上级可管理下级，同级不可互相管理
- **数据范围**: 根据用户权限显示可访问的组织

#### 实现文件
- **后端**: `OrganizationController.php`, `Organization.php`
- **前端**: `OrganizationList.vue`, `OrganizationForm.vue`
- **状态管理**: `organization.js`
- **API服务**: `organization.js`

### 4. 用户管理系统 ✅

#### 功能特性
- **用户CRUD**: 完整的用户管理功能
- **组织关联**: 用户与组织的多对多关系
- **角色分配**: 用户角色管理
- **权限继承**: 基于组织层级的权限继承

#### 实现文件
- **后端**: `UserController.php`, `User.php`
- **前端**: `UserList.vue`, `UserForm.vue`
- **状态管理**: `user.js`

### 5. 角色权限管理 ✅

#### 功能特性
- **角色定义**: 10个预定义角色（系统管理员到普通教师）
- **权限分配**: 12个核心权限模块
- **动态权限**: 支持运行时权限检查
- **权限继承**: 角色权限的层级继承

#### 实现文件
- **后端**: `RoleController.php`, `PermissionController.php`
- **前端**: `RoleList.vue`, `RoleForm.vue`
- **状态管理**: `role.js`

### 6. 系统管理界面 ✅

#### 仪表板功能
- **统计概览**: 组织、用户、角色、权限数量统计
- **用户信息**: 当前登录用户详细信息
- **权限展示**: 用户角色和权限范围
- **图表展示**: ECharts数据可视化

#### 布局系统
- **响应式布局**: 侧边栏 + 主内容区域
- **导航菜单**: 基于权限的动态菜单
- **面包屑**: 页面导航路径
- **用户操作**: 用户信息和登出功能

## 数据库结构

### 核心数据表
1. **organizations** - 组织机构表（支持树形结构）
2. **users** - 用户表（扩展字段丰富）
3. **roles** - 角色表（支持软删除）
4. **permissions** - 权限表（树形结构 + 软删除）
5. **role_user** - 用户角色关联表
6. **permission_role** - 角色权限关联表
7. **user_organizations** - 用户组织关联表
8. **user_permissions** - 用户直接权限表

### 示例数据
- **组织架构**: 河北省 → 石家庄市 → 藁城区 → 廉州学区 → 东城小学
- **测试用户**: 3个不同权限级别的用户
- **角色权限**: 完整的权限矩阵配置

## API接口规范

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出  
- `GET /api/auth/user` - 获取当前用户信息

### 组织机构接口
- `GET /api/organizations` - 获取组织列表
- `GET /api/organizations/tree` - 获取组织树
- `POST /api/organizations` - 创建组织
- `GET /api/organizations/{id}` - 获取组织详情
- `PUT /api/organizations/{id}` - 更新组织
- `DELETE /api/organizations/{id}` - 删除组织

### 用户管理接口
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `GET /api/users/{id}` - 获取用户详情
- `PUT /api/users/{id}` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

### 角色权限接口
- `GET /api/roles` - 获取角色列表
- `GET /api/permissions` - 获取权限列表
- `GET /api/permissions/tree` - 获取权限树
- `GET /api/permissions/menu` - 获取菜单权限

## 前端状态管理

### Pinia Stores
- **auth.js**: 用户认证状态、token管理、权限信息
- **organization.js**: 组织机构数据、树形结构
- **user.js**: 用户列表、当前用户信息
- **role.js**: 角色数据管理

### API服务层
- **request.js**: Axios配置、请求/响应拦截器
- **auth.js**: 认证相关API
- **organization.js**: 组织机构API
- **user.js**: 用户管理API
- **role.js**: 角色管理API

## 权限控制实现

### 后端权限控制
```php
// 中间件使用
Route::middleware(['auth:sanctum', 'permission:organization.view'])
    ->get('/organizations', [OrganizationController::class, 'index']);

// 权限检查
if (!$user->hasPermission('organization.create', $organizationId)) {
    return response()->json(['message' => '权限不足'], 403);
}
```

### 前端权限控制
```vue
<!-- 权限指令 -->
<el-button v-permission="'organization.create'">创建组织</el-button>
<div v-role="'admin'">管理员专用内容</div>

<!-- 编程式权限检查 -->
if (authStore.hasPermission('organization.edit')) {
  // 显示编辑功能
}
```

## 开发环境配置

### 启动命令
```bash
# 后端启动
cd backend
php artisan serve --host=localhost --port=8000

# 前端启动  
cd frontend
npm run dev

# 数据库重置
cd backend
php artisan migrate:fresh --seed
```

### 访问地址
- **前端**: http://localhost:5173
- **后端API**: http://localhost:8000/api
- **健康检查**: http://localhost:8000/api/health

## 下一步开发计划

### 待实现功能模块
1. **实验过程管理** - 实验计划、记录、审核
2. **基础数据管理** - 实验目录、设备物料
3. **数据看板** - 统计分析、可视化图表
4. **督导管理** - 督导任务、报告管理
5. **资源共享** - 实验资源分享平台
6. **通知管理** - 系统通知、消息推送
7. **数据备份** - 备份恢复功能
8. **移动端适配** - 响应式设计优化

### 技术优化
- **性能优化**: 数据库查询优化、前端懒加载
- **安全加固**: 输入验证、SQL注入防护
- **测试覆盖**: 单元测试、集成测试
- **文档完善**: API文档、用户手册

## 关键代码实现细节

### 用户模型核心方法
```php
// User.php - 权限检查方法
public function hasPermission($permission, $organizationId = null): bool
{
    // 通过角色获取权限
    foreach ($this->roles as $role) {
        if ($role->hasPermission($permission, $organizationId)) {
            return true;
        }
    }

    // 检查直接分配的权限
    return $this->permissions()
        ->where('name', $permission)
        ->where(function($query) use ($organizationId) {
            $query->whereNull('organization_id')
                  ->orWhere('organization_id', $organizationId);
        })->exists();
}

// 获取数据访问范围
public function getDataAccessScope(): array
{
    $user = $this;

    // 系统管理员有全局权限
    if ($user->hasRole('system_admin')) {
        return ['type' => 'global', 'organizations' => []];
    }

    // 获取用户所属组织及其下级组织
    $accessibleOrgs = collect();
    foreach ($user->organizations as $org) {
        $accessibleOrgs->push($org->id);
        $descendants = $org->getDescendants();
        $accessibleOrgs = $accessibleOrgs->merge($descendants->pluck('id'));
    }

    return [
        'type' => 'specific',
        'organizations' => $accessibleOrgs->unique()->values()->toArray()
    ];
}
```

### 组织模型树形结构
```php
// Organization.php - 树形结构方法
public static function getTree($parentId = null)
{
    $organizations = static::where('parent_id', $parentId)
        ->active()
        ->orderBy('sort_order')
        ->get();

    foreach ($organizations as $organization) {
        $organization->children_tree = static::getTree($organization->id);
    }

    return $organizations;
}

// 获取所有后代组织
public function getDescendants()
{
    $descendants = collect();

    foreach ($this->children as $child) {
        $descendants->push($child);
        $descendants = $descendants->merge($child->getDescendants());
    }

    return $descendants;
}
```

### 前端权限指令实现
```javascript
// utils/permission.js
export const permission = {
  mounted(el, binding) {
    const { value } = binding
    const authStore = useAuthStore()

    if (!authStore.hasPermission(value)) {
      el.style.display = 'none'
    }
  },
  updated(el, binding) {
    const { value } = binding
    const authStore = useAuthStore()

    if (!authStore.hasPermission(value)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

export const role = {
  mounted(el, binding) {
    const { value } = binding
    const authStore = useAuthStore()

    if (!authStore.hasRole(value)) {
      el.style.display = 'none'
    }
  }
}
```

### API请求拦截器
```javascript
// api/request.js
request.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

request.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const { response } = error

    if (response?.status === 401) {
      ElMessage.error('登录已过期，请重新登录')
      const authStore = useAuthStore()
      authStore.logout()
      window.location.href = '/login'
    }

    return Promise.reject(error)
  }
)
```

## 数据库设计详情

### 组织机构表结构
```sql
CREATE TABLE `organizations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '组织名称',
  `code` varchar(50) DEFAULT NULL COMMENT '组织编码',
  `type` enum('province','city','district','zone','school') NOT NULL COMMENT '组织类型',
  `level` tinyint unsigned NOT NULL COMMENT '组织层级(1-5)',
  `parent_id` bigint unsigned DEFAULT NULL COMMENT '父级组织ID',
  `path` varchar(500) DEFAULT NULL COMMENT '组织路径',
  `sort_order` int unsigned DEFAULT '0' COMMENT '排序',
  `status` enum('active','inactive') DEFAULT 'active' COMMENT '状态',
  `description` text COMMENT '描述',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `address` varchar(200) DEFAULT NULL COMMENT '地址',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `organizations_parent_id_index` (`parent_id`),
  KEY `organizations_type_index` (`type`),
  KEY `organizations_level_index` (`level`)
);
```

### 用户表扩展字段
```sql
ALTER TABLE `users` ADD COLUMN (
  `username` varchar(50) UNIQUE NOT NULL COMMENT '用户名',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `gender` enum('male','female','other') DEFAULT 'other' COMMENT '性别',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `address` varchar(200) DEFAULT NULL COMMENT '地址',
  `employee_id` varchar(50) DEFAULT NULL COMMENT '工号',
  `user_type` enum('admin','teacher','student') DEFAULT 'teacher' COMMENT '用户类型',
  `department` varchar(100) DEFAULT NULL COMMENT '部门',
  `position` varchar(100) DEFAULT NULL COMMENT '职位',
  `title` varchar(100) DEFAULT NULL COMMENT '职称',
  `hire_date` date DEFAULT NULL COMMENT '入职日期',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `status` enum('active','inactive','locked') DEFAULT 'active' COMMENT '状态',
  `preferences` json DEFAULT NULL COMMENT '用户偏好设置',
  `remarks` text COMMENT '备注'
);
```

## 测试数据说明

### 组织架构示例
```
河北省 (province, level=1)
└── 石家庄市 (city, level=2)
    └── 藁城区 (district, level=3)
        └── 廉州学区 (zone, level=4)
            └── 东城小学 (school, level=5)
```

### 角色权限矩阵
| 角色 | 权限级别 | 管理范围 | 主要权限 |
|------|----------|----------|----------|
| 系统管理员 | 1 | 全系统 | 所有权限 |
| 省级管理员 | 1 | 全省 | 省级管理权限 |
| 市级管理员 | 2 | 本市 | 市级管理权限 |
| 区县管理员 | 3 | 本区县 | 区县管理权限 |
| 学区管理员 | 4 | 本学区 | 学区管理权限 |
| 学校管理员 | 5 | 本校 | 学校管理权限 |
| 教师 | 5 | 本校 | 基础教学权限 |
| 学生 | 5 | 本校 | 学习权限 |

## 开发规范和最佳实践

### 后端开发规范
1. **控制器**: 使用Resource控制器，遵循RESTful设计
2. **模型**: 使用Eloquent关系，定义访问器和修改器
3. **验证**: 使用FormRequest进行数据验证
4. **中间件**: 权限检查和数据过滤
5. **异常处理**: 统一的错误响应格式

### 前端开发规范
1. **组件**: 使用Composition API，单一职责原则
2. **状态管理**: Pinia store模块化管理
3. **路由**: 懒加载和权限守卫
4. **样式**: Scoped CSS，响应式设计
5. **错误处理**: 统一的错误提示和处理

### API设计规范
```json
// 成功响应格式
{
  "message": "操作成功",
  "data": {...},
  "code": 200
}

// 错误响应格式
{
  "message": "错误信息",
  "code": 400,
  "errors": {...}
}

// 分页响应格式
{
  "message": "获取成功",
  "data": {
    "data": [...],
    "current_page": 1,
    "total": 100,
    "per_page": 15
  },
  "code": 200
}
```

## 环境配置文件

### 后端环境配置 (.env)
```env
APP_NAME="实验教学管理系统"
APP_ENV=local
APP_KEY=base64:generated_key
APP_DEBUG=true
APP_URL=http://localhost:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=gcqets
DB_USERNAME=root
DB_PASSWORD=liningyu2000

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

SANCTUM_STATEFUL_DOMAINS=localhost:5173,127.0.0.1:5173
```

### 前端配置 (vite.config.js)
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      }
    }
  }
})
```

## 常见问题和解决方案

### 1. 跨域问题
**问题**: 前端请求后端API时出现CORS错误
**解决**:
- 后端配置SANCTUM_STATEFUL_DOMAINS
- 前端使用Vite代理配置
- Laravel CORS中间件配置

### 2. 权限验证失败
**问题**: API返回403权限不足
**解决**:
- 检查用户是否已登录
- 验证用户角色和权限配置
- 确认数据库权限数据完整性

### 3. 数据库连接失败
**问题**: Laravel无法连接MySQL数据库
**解决**:
- 确认XAMPP MySQL服务已启动
- 检查.env文件数据库配置
- 验证数据库用户权限

### 4. 前端路由404
**问题**: 刷新页面出现404错误
**解决**:
- 配置服务器重写规则
- 使用Hash路由模式
- 检查路由配置是否正确

## 性能优化建议

### 数据库优化
1. **索引优化**: 为常用查询字段添加索引
2. **查询优化**: 使用Eloquent关系预加载
3. **分页查询**: 大数据量使用分页
4. **缓存策略**: Redis缓存热点数据

### 前端优化
1. **懒加载**: 路由和组件按需加载
2. **图片优化**: 压缩和WebP格式
3. **打包优化**: Vite代码分割
4. **缓存策略**: HTTP缓存和本地存储

## 安全考虑

### 后端安全
1. **输入验证**: 所有用户输入严格验证
2. **SQL注入**: 使用Eloquent ORM防护
3. **XSS防护**: 输出转义和CSP策略
4. **CSRF防护**: Laravel内置CSRF保护
5. **权限控制**: 细粒度权限验证

### 前端安全
1. **Token安全**: 安全存储和传输
2. **路由守卫**: 权限验证和重定向
3. **数据验证**: 前端表单验证
4. **敏感信息**: 避免在前端暴露敏感数据

## 部署指南

### 开发环境部署
```bash
# 1. 克隆项目
git clone <repository-url> gcqets
cd gcqets

# 2. 后端部署
cd backend
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate:fresh --seed
php artisan serve --host=localhost --port=8000

# 3. 前端部署
cd ../frontend
npm install
npm run dev
```

### 生产环境部署
```bash
# 1. 后端生产配置
cd backend
composer install --optimize-autoloader --no-dev
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 2. 前端生产构建
cd ../frontend
npm run build

# 3. Web服务器配置
# 配置Nginx/Apache虚拟主机
# 设置SSL证书
# 配置防火墙规则
```

## 监控和日志

### 日志配置
```php
// config/logging.php
'channels' => [
    'daily' => [
        'driver' => 'daily',
        'path' => storage_path('logs/laravel.log'),
        'level' => env('LOG_LEVEL', 'debug'),
        'days' => 14,
    ],
    'permission' => [
        'driver' => 'daily',
        'path' => storage_path('logs/permission.log'),
        'level' => 'info',
        'days' => 30,
    ],
]
```

### 错误监控
- **后端**: Laravel Log系统
- **前端**: Console错误捕获
- **API**: 请求响应日志
- **性能**: 查询时间监控

## 备份策略

### 数据库备份
```bash
# 每日自动备份
mysqldump -u root -p gcqets > backup_$(date +%Y%m%d).sql

# 定期清理旧备份
find /backup -name "*.sql" -mtime +30 -delete
```

### 代码备份
- **版本控制**: Git仓库管理
- **定期推送**: 远程仓库同步
- **分支管理**: 功能分支开发

---

**文档维护说明**:
- 本文档应随项目开发进度实时更新
- 新增功能需要补充对应的实现细节
- 重要变更需要更新版本号和修改日期
- 建议每个开发阶段结束后进行文档审查和完善

**AI开发助手使用指南**:
1. 开发新功能前，先阅读本文档了解现有架构
2. 参考已实现功能的代码结构和命名规范
3. 遵循既定的API设计和数据库设计原则
4. 新增功能后及时更新本文档内容
5. 遇到问题时参考常见问题解决方案
