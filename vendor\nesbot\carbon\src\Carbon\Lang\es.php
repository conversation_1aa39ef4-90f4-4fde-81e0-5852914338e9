<?php

/*
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array(
    'year' => ':count año|:count años',
    'y' => ':count año|:count años',
    'month' => ':count mes|:count meses',
    'm' => ':count mes|:count meses',
    'week' => ':count semana|:count semanas',
    'w' => ':count semana|:count semanas',
    'day' => ':count día|:count días',
    'd' => ':count día|:count días',
    'hour' => ':count hora|:count horas',
    'h' => ':count hora|:count horas',
    'minute' => ':count minuto|:count minutos',
    'min' => ':count minuto|:count minutos',
    'second' => ':count segundo|:count segundos',
    's' => ':count segundo|:count segundos',
    'ago' => 'hace :time',
    'from_now' => 'dentro de :time',
    'after' => ':time después',
    'before' => ':time antes',
    'diff_now' => 'ahora mismo',
    'diff_yesterday' => 'ayer',
    'diff_tomorrow' => 'mañana',
    'diff_before_yesterday' => 'antier',
    'diff_after_tomorrow' => 'pasado mañana',
);
