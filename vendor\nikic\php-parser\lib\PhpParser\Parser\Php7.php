<?php

namespace Php<PERSON>ars<PERSON>\Parser;

use <PERSON>p<PERSON><PERSON><PERSON>\Error;
use Php<PERSON><PERSON><PERSON>\Node;
use Php<PERSON>arser\Node\Expr;
use PhpParser\Node\Name;
use PhpParser\Node\Scalar;
use Php<PERSON>arser\Node\Stmt;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar files grammar/php5.y or grammar/php7.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class Php7 extends \PhpParser\ParserAbstract
{
    protected $tokenToSymbolMapSize = 396;
    protected $actionTableSize = 1241;
    protected $gotoTableSize = 629;

    protected $invalidSymbol = 168;
    protected $errorSymbol = 1;
    protected $defaultAction = -32766;
    protected $unexpectedTokenRule = 32767;

    protected $YY2TBLSTATE = 435;
    protected $numNonLeafStates = 737;

    protected $symbolToName = array(
        "EOF",
        "error",
        "T_THROW",
        "T_INCLUDE",
        "T_INCLUDE_ONCE",
        "T_EVAL",
        "T_REQUIRE",
        "T_REQUIRE_ONCE",
        "','",
        "T_LOGICAL_OR",
        "T_LOGICAL_XOR",
        "T_LOGICAL_AND",
        "T_PRINT",
        "T_YIELD",
        "T_DOUBLE_ARROW",
        "T_YIELD_FROM",
        "'='",
        "T_PLUS_EQUAL",
        "T_MINUS_EQUAL",
        "T_MUL_EQUAL",
        "T_DIV_EQUAL",
        "T_CONCAT_EQUAL",
        "T_MOD_EQUAL",
        "T_AND_EQUAL",
        "T_OR_EQUAL",
        "T_XOR_EQUAL",
        "T_SL_EQUAL",
        "T_SR_EQUAL",
        "T_POW_EQUAL",
        "T_COALESCE_EQUAL",
        "'?'",
        "':'",
        "T_COALESCE",
        "T_BOOLEAN_OR",
        "T_BOOLEAN_AND",
        "'|'",
        "'^'",
        "T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG",
        "T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG",
        "T_IS_EQUAL",
        "T_IS_NOT_EQUAL",
        "T_IS_IDENTICAL",
        "T_IS_NOT_IDENTICAL",
        "T_SPACESHIP",
        "'<'",
        "T_IS_SMALLER_OR_EQUAL",
        "'>'",
        "T_IS_GREATER_OR_EQUAL",
        "T_SL",
        "T_SR",
        "'+'",
        "'-'",
        "'.'",
        "'*'",
        "'/'",
        "'%'",
        "'!'",
        "T_INSTANCEOF",
        "'~'",
        "T_INC",
        "T_DEC",
        "T_INT_CAST",
        "T_DOUBLE_CAST",
        "T_STRING_CAST",
        "T_ARRAY_CAST",
        "T_OBJECT_CAST",
        "T_BOOL_CAST",
        "T_UNSET_CAST",
        "'@'",
        "T_POW",
        "'['",
        "T_NEW",
        "T_CLONE",
        "T_EXIT",
        "T_IF",
        "T_ELSEIF",
        "T_ELSE",
        "T_ENDIF",
        "T_LNUMBER",
        "T_DNUMBER",
        "T_STRING",
        "T_STRING_VARNAME",
        "T_VARIABLE",
        "T_NUM_STRING",
        "T_INLINE_HTML",
        "T_ENCAPSED_AND_WHITESPACE",
        "T_CONSTANT_ENCAPSED_STRING",
        "T_ECHO",
        "T_DO",
        "T_WHILE",
        "T_ENDWHILE",
        "T_FOR",
        "T_ENDFOR",
        "T_FOREACH",
        "T_ENDFOREACH",
        "T_DECLARE",
        "T_ENDDECLARE",
        "T_AS",
        "T_SWITCH",
        "T_MATCH",
        "T_ENDSWITCH",
        "T_CASE",
        "T_DEFAULT",
        "T_BREAK",
        "T_CONTINUE",
        "T_GOTO",
        "T_FUNCTION",
        "T_FN",
        "T_CONST",
        "T_RETURN",
        "T_TRY",
        "T_CATCH",
        "T_FINALLY",
        "T_USE",
        "T_INSTEADOF",
        "T_GLOBAL",
        "T_STATIC",
        "T_ABSTRACT",
        "T_FINAL",
        "T_PRIVATE",
        "T_PROTECTED",
        "T_PUBLIC",
        "T_READONLY",
        "T_VAR",
        "T_UNSET",
        "T_ISSET",
        "T_EMPTY",
        "T_HALT_COMPILER",
        "T_CLASS",
        "T_TRAIT",
        "T_INTERFACE",
        "T_ENUM",
        "T_EXTENDS",
        "T_IMPLEMENTS",
        "T_OBJECT_OPERATOR",
        "T_NULLSAFE_OBJECT_OPERATOR",
        "T_LIST",
        "T_ARRAY",
        "T_CALLABLE",
        "T_CLASS_C",
        "T_TRAIT_C",
        "T_METHOD_C",
        "T_FUNC_C",
        "T_LINE",
        "T_FILE",
        "T_START_HEREDOC",
        "T_END_HEREDOC",
        "T_DOLLAR_OPEN_CURLY_BRACES",
        "T_CURLY_OPEN",
        "T_PAAMAYIM_NEKUDOTAYIM",
        "T_NAMESPACE",
        "T_NS_C",
        "T_DIR",
        "T_NS_SEPARATOR",
        "T_ELLIPSIS",
        "T_NAME_FULLY_QUALIFIED",
        "T_NAME_QUALIFIED",
        "T_NAME_RELATIVE",
        "T_ATTRIBUTE",
        "';'",
        "']'",
        "'{'",
        "'}'",
        "'('",
        "')'",
        "'`'",
        "'\"'",
        "'$'"
    );

    protected $tokenToSymbol = array(
            0,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,   56,  166,  168,  167,   55,  168,  168,
          163,  164,   53,   50,    8,   51,   52,   54,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,   31,  159,
           44,   16,   46,   30,   68,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,   70,  168,  160,   36,  168,  165,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  161,   35,  162,   58,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,    1,    2,    3,    4,
            5,    6,    7,    9,   10,   11,   12,   13,   14,   15,
           17,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   28,   29,   32,   33,   34,   37,   38,   39,   40,
           41,   42,   43,   45,   47,   48,   49,   57,   59,   60,
           61,   62,   63,   64,   65,   66,   67,   69,   71,   72,
           73,   74,   75,   76,   77,   78,   79,   80,   81,   82,
           83,   84,   85,   86,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,  134,  135,  136,  137,  138,  139,  140,  141,  142,
          143,  144,  145,  146,  147,  148,  149,  150,  151,  152,
          153,  154,  155,  156,  157,  158
    );

    protected $action = array(
          133,  134,  135,  580,  136,  137,    0,  749,  750,  751,
          138,   38,  327,-32766,-32766,-32766,-32766,-32766,-32766,-32767,
        -32767,-32767,-32767,  102,  103,  104,  105,  106, 1110, 1111,
         1112, 1109, 1108, 1107, 1113,  743,  742,-32766, 1233,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,-32767,-32767,-32767,-32767,
        -32767,    2,  107,  108,  109,  752,  274,  381,  380,-32766,
        -32766,-32766,-32766,  104,  105,  106, 1025,  423,  110,  265,
          139,  402,  756,  757,  758,  759,  467,  468,  429,  939,
          291,-32766,  287,-32766,-32766,  760,  761,  762,  763,  764,
          765,  766,  767,  768,  769,  770,  790,  581,  791,  792,
          793,  794,  782,  783,  344,  345,  785,  786,  771,  772,
          773,  775,  776,  777,  355,  817,  818,  819,  820,  821,
          582,  778,  779,  583,  584,  811,  802,  800,  801,  814,
          797,  798,  688, -545,  585,  586,  796,  587,  588,  589,
          590,  591,  592, -328, -593, -367, 1235, -367,  799,  593,
          594, -593,  140,-32766,-32766,-32766,  133,  134,  135,  580,
          136,  137, 1058,  749,  750,  751,  138,   38,  689, 1021,
         1020, 1019, 1022,  390,-32766,    7,-32766,-32766,-32766,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,  379,  380, 1034,  690,
          691,  743,  742,-32766,-32766,-32766,  423, -545, -545, -590,
        -32766,-32766,-32766, 1033,-32766,  127, -590, 1237, 1236, 1238,
         1319,  752, -545,  290,-32766,  283,-32766,-32766,-32766,-32766,
        -32766, 1237, 1236, 1238, -545,  265,  139,  402,  756,  757,
          758,  759,   16,  482,  429,  459,  460,  461,  298,  723,
           35,  760,  761,  762,  763,  764,  765,  766,  767,  768,
          769,  770,  790,  581,  791,  792,  793,  794,  782,  783,
          344,  345,  785,  786,  771,  772,  773,  775,  776,  777,
          355,  817,  818,  819,  820,  821,  582,  778,  779,  583,
          584,  811,  802,  800,  801,  814,  797,  798,  129,  825,
          585,  586,  796,  587,  588,  589,  590,  591,  592, -328,
           83,   84,   85, -593,  799,  593,  594, -593,  149,  774,
          744,  745,  746,  747,  748,  825,  749,  750,  751,  787,
          788,   37,  145,   86,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  291,  274,  836,
          254, 1110, 1111, 1112, 1109, 1108, 1107, 1113, -590,  861,
          110,  862, -590,  483,  752,-32766,-32766,-32766,-32766,-32766,
          142,  604, 1086,  743,  742, 1263,  326,  988,  753,  754,
          755,  756,  757,  758,  759,  309,-32766,  823,-32766,-32766,
        -32766,-32766,  242,  554,  760,  761,  762,  763,  764,  765,
          766,  767,  768,  769,  770,  790,  813,  791,  792,  793,
          794,  782,  783,  784,  812,  785,  786,  771,  772,  773,
          775,  776,  777,  816,  817,  818,  819,  820,  821,  822,
          778,  779,  780,  781,  811,  802,  800,  801,  814,  797,
          798,  311,  941,  789,  795,  796,  803,  804,  806,  805,
          807,  808,  323,  610, 1275, 1034,  834,  799,  810,  809,
           50,   51,   52,  513,   53,   54,  861,  241,  862,  919,
           55,   56, -111,   57,-32766,-32766,-32766, -111,  827, -111,
          290, 1303, 1348,  356,  305, 1349,  339, -111, -111, -111,
         -111, -111, -111, -111, -111,-32766, -194,-32766,-32766,-32766,
         -193,  957,  958,  830,  -86,  989,  959,  835,   58,   59,
          340,  429,  953, -544,   60,  833,   61,  247,  248,   62,
           63,   64,   65,   66,   67,   68,   69, 1242,   28,  267,
           70,  445,  514, -342,-32766,  141, 1269, 1270,  515,  919,
          834,  326, -272,  919, 1267,   42,   25,  516,  941,  517,
           14,  518,  909,  519,  829,  369,  520,  521,  373,  710,
         1034,   44,   45,  446,  376,  375,  388,   46,  522,  713,
          -86,  441, 1102,  367,  338, -543,  442, -544, -544,  831,
         1228,  443,  524,  525,  526,  290, 1237, 1236, 1238,  361,
         1031,  444, -544, 1088,  527,  528,  840, 1256, 1257, 1258,
         1259, 1253, 1254,  297, -544,  151, -550, -584,  834, 1260,
         1255, -584, 1034, 1237, 1236, 1238,  298, -154, -154, -154,
          152,   71,  909,  321,  322,  326,  909,  921, 1031,  708,
          834,  154, -154, 1338, -154,  155, -154,  283, -154, -543,
         -543,   82, 1233, 1087, 1323,  735,  156,  326,  374,  158,
         1034, 1322, -194,  -79, -543,  -88, -193,  743,  742,  957,
          958,  654,   26,-32766,  523,  -51, -543,   33, -549,  895,
          953, -111, -111, -111,   32,  111,  112,  113,  114,  115,
          116,  117,  118,  119,  120,  121,  122,  123,  -59,   75,
           28,  673,  674,  326,  -58,   36,  250,  921,  124,  708,
          125,  921,  834,  708, -154,  130, 1267,  131,-32766, -547,
          144, -542,  150,  406, 1235,  377,  378, 1147, 1149,  382,
          383,-32766,-32766,-32766,  -85,-32766, 1057,-32766, -542,-32766,
          645,  646,-32766,  159,  160,  161, 1233,-32766,-32766,-32766,
          162,  -79, 1228,-32766,-32766,  743,  742,  163, -302,-32766,
          420,  -75,   -4,  919,  -73,  287,  527,  528,-32766, 1256,
         1257, 1258, 1259, 1253, 1254,  -72,  -71,  -70,  -69,  -68,
          -67, 1260, 1255, -547, -547, -542, -542,  743,  742,  -66,
          -47,  -18,-32766,   73,  148,  919,  322,  326, 1235,  273,
         -542,  284, -542, -542,  724,-32766,-32766,-32766,  727,-32766,
         -547,-32766, -542,-32766,  918,  147,-32766, -542,  288,  289,
         -298,-32766,-32766,-32766,-32766,  714,  279,-32766,-32766, -542,
         1235,  280,  285,-32766,  420,   48,  286,-32766,-32766,-32766,
          332,-32766,-32766,-32766,  292,-32766,  909,  293,-32766,  935,
          274, 1031,  919,-32766,-32766,-32766,  110,  683,  132,-32766,
        -32766,  834,  146,-32766,  560,-32766,  420,  660,  374,  825,
          436, 1350,   74, 1034,-32766,  296,  655, 1117,  909,  957,
          958,  306,  715,  699,  523,  556,  303,   13,  310,  853,
          953, -111, -111, -111,  701,  464,  493,  954,  283,  299,
          300,-32766,   49,  676,  919,  304,  661, 1235,  677,  937,
         1274,-32766,   10, 1264,-32766,-32766,-32766,  643,-32766,  919,
        -32766,  921,-32766,  708,   -4,-32766,  126,   34,  919,  566,
        -32766,-32766,-32766,-32766,    0,  909,-32766,-32766,    0, 1235,
          919,    0,-32766,  420,    0,    0,-32766,-32766,-32766,  718,
        -32766,-32766,-32766,  921,-32766,  708, 1034,-32766,  725, 1276,
            0,  488,-32766,-32766,-32766,-32766,  301,  302,-32766,-32766,
         -507, 1235,  572, -497,-32766,  420,  608,    8,-32766,-32766,
        -32766,  372,-32766,-32766,-32766,   17,-32766,  909,  371,-32766,
          833,  298,  320,  128,-32766,-32766,-32766,   40,  370,   41,
        -32766,-32766,  909, -250, -250, -250,-32766,  420,  732,  374,
          974,  909,  708,  733,  900,-32766,  998,  975,  405,  982,
          957,  958,  972,  909,  983,  523,  898,  970, 1091, 1094,
          895,  953, -111, -111, -111,   28, 1095, 1092, 1093, -249,
         -249, -249, 1242, 1099,  709,  374,  845,  834, 1289, 1307,
         1341, 1267,  648, 1268,  712,  716,  957,  958,  717, 1242,
          719,  523,  921,  720,  708, -250,  895,  953, -111, -111,
         -111,  721,  -16,  722,  726,  711, -511,  921,  409,  708,
         -578, 1233,  729,  896, 1345, 1347,  921, 1228,  708, -577,
          856,  855,  947,  990, 1346,  946,  944,  945,  921,  948,
          708, -249,  528, 1219, 1256, 1257, 1258, 1259, 1253, 1254,
          928,  938,  926,  980,  981,  632, 1260, 1255, 1344, 1301,
        -32766, 1290, 1308,  834, 1317, -275, 1235, -576,   73, -550,
         -549,  322,  326,-32766,-32766,-32766, -548,-32766, -491,-32766,
          834,-32766,    1,   29,-32766,   30,   39,   43,   47,-32766,
        -32766,-32766,   72,   76,   77,-32766,-32766, 1233, -111, -111,
           78,-32766,  420, -111,   79,   80,   81,  143,  153, -111,
        -32766,  157,  246,  328, 1233, -111, -111,  356,-32766,  357,
         -111,  358,  359,  360,  361,  362, -111,  363,  364,  365,
          366,  368,  437,    0, -273,-32766, -272,   19,   20,  298,
           21,   22,   24,  404,   75, 1204,  484,  485,  326,  492,
            0,  495,  496,  497,  498,  502,  298,  503,  504,  511,
          694,   75,    0, 1246, 1187,  326, 1265, 1060, 1059, 1040,
         1223, 1036, -277, -103,   18,   23,   27,  295,  403,  601,
          605,  634,  700, 1191, 1241, 1188, 1320,    0,    0,    0,
          326
    );

    protected $actionCheck = array(
            2,    3,    4,    5,    6,    7,    0,    9,   10,   11,
           12,   13,   70,    9,   10,   11,    9,   10,   11,   44,
           45,   46,   47,   48,   49,   50,   51,   52,  116,  117,
          118,  119,  120,  121,  122,   37,   38,   30,  116,   32,
           33,   34,   35,   36,   37,   38,   39,   40,   41,   42,
           43,    8,   53,   54,   55,   57,   57,  106,  107,  137,
            9,   10,   11,   50,   51,   52,    1,  116,   69,   71,
           72,   73,   74,   75,   76,   77,  134,  135,   80,    1,
           30,   30,   30,   32,   33,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,  123,  124,  125,  126,  127,  128,  129,  130,  131,
          132,  133,   80,   70,  136,  137,  138,  139,  140,  141,
          142,  143,  144,    8,    1,  106,   80,  108,  150,  151,
          152,    8,  154,    9,   10,   11,    2,    3,    4,    5,
            6,    7,  164,    9,   10,   11,   12,   13,  116,  119,
          120,  121,  122,  106,   30,  108,   32,   33,   34,   35,
           36,   37,   38,    9,   10,   11,  106,  107,  138,  137,
          138,   37,   38,    9,   10,   11,  116,  134,  135,    1,
            9,   10,   11,  137,   30,   14,    8,  155,  156,  157,
            1,   57,  149,  163,   30,  163,   32,   33,   34,   35,
           36,  155,  156,  157,  161,   71,   72,   73,   74,   75,
           76,   77,    8,   31,   80,  129,  130,  131,  158,  161,
            8,   87,   88,   89,   90,   91,   92,   93,   94,   95,
           96,   97,   98,   99,  100,  101,  102,  103,  104,  105,
          106,  107,  108,  109,  110,  111,  112,  113,  114,  115,
          116,  117,  118,  119,  120,  121,  122,  123,  124,  125,
          126,  127,  128,  129,  130,  131,  132,  133,    8,   80,
          136,  137,  138,  139,  140,  141,  142,  143,  144,  164,
            9,   10,   11,  160,  150,  151,  152,  164,  154,    2,
            3,    4,    5,    6,    7,   80,    9,   10,   11,   12,
           13,   30,    8,   32,   33,   34,   35,   36,   37,   38,
           39,   40,   41,   42,   43,   44,   45,   46,   47,   48,
           49,   50,   51,   52,   53,   54,   55,   30,   57,    1,
            8,  116,  117,  118,  119,  120,  121,  122,  160,  106,
           69,  108,  164,  161,   57,    9,   10,   11,    9,   10,
          161,    1,    1,   37,   38,    1,  167,   31,   71,   72,
           73,   74,   75,   76,   77,    8,   30,   80,   32,   33,
           34,   35,   14,   85,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,    8,  122,  136,  137,  138,  139,  140,  141,  142,
          143,  144,    8,   51,  146,  138,   82,  150,  151,  152,
            2,    3,    4,    5,    6,    7,  106,   97,  108,    1,
           12,   13,  101,   15,    9,   10,   11,  106,   80,  108,
          163,    1,   80,  163,  113,   83,    8,  116,  117,  118,
          119,  120,  121,  122,  123,   30,    8,   32,   33,   34,
            8,  117,  118,   80,   31,  159,  122,  159,   50,   51,
            8,   80,  128,   70,   56,  155,   58,   59,   60,   61,
           62,   63,   64,   65,   66,   67,   68,    1,   70,   71,
           72,   73,   74,  162,    9,  161,   78,   79,   80,    1,
           82,  167,  164,    1,   86,   87,   88,   89,  122,   91,
          101,   93,   84,   95,  156,    8,   98,   99,    8,  161,
          138,  103,  104,  105,  106,  107,    8,  109,  110,   31,
           97,    8,  123,  115,  116,   70,    8,  134,  135,  156,
          122,    8,  124,  125,  126,  163,  155,  156,  157,  163,
          116,    8,  149,  162,  136,  137,    8,  139,  140,  141,
          142,  143,  144,  145,  161,   14,  163,  160,   82,  151,
          152,  164,  138,  155,  156,  157,  158,   75,   76,   77,
           14,  163,   84,  165,  166,  167,   84,  159,  116,  161,
           82,   14,   90,   85,   92,   14,   94,  163,   96,  134,
          135,  161,  116,  159,    1,  161,   14,  167,  106,   14,
          138,    8,  164,   16,  149,   31,  164,   37,   38,  117,
          118,   75,   76,  137,  122,   31,  161,   14,  163,  127,
          128,  129,  130,  131,   16,   17,   18,   19,   20,   21,
           22,   23,   24,   25,   26,   27,   28,   29,   16,  163,
           70,   75,   76,  167,   16,  147,  148,  159,   16,  161,
           16,  159,   82,  161,  162,   16,   86,   16,   74,   70,
           16,   70,  101,  102,   80,  106,  107,   59,   60,  106,
          107,   87,   88,   89,   31,   91,    1,   93,   70,   95,
          111,  112,   98,   16,   16,   16,  116,  103,  104,  105,
           16,   31,  122,  109,  110,   37,   38,   16,   35,  115,
          116,   31,    0,    1,   31,   30,  136,  137,  124,  139,
          140,  141,  142,  143,  144,   31,   31,   31,   31,   31,
           31,  151,  152,  134,  135,  134,  135,   37,   38,   31,
           31,   31,   74,  163,   31,    1,  166,  167,   80,   31,
          149,   31,  134,  135,   31,   87,   88,   89,   31,   91,
          161,   93,  161,   95,   31,   31,   98,  149,   37,   37,
           35,  103,  104,  105,   74,   31,   35,  109,  110,  161,
           80,   35,   35,  115,  116,   70,   35,   87,   88,   89,
           35,   91,  124,   93,   37,   95,   84,   37,   98,   38,
           57,  116,    1,  103,  104,  105,   69,   77,   31,  109,
          110,   82,   70,   85,   89,  115,  116,   96,  106,   80,
          108,   83,  154,  138,  124,  113,   90,   82,   84,  117,
          118,  114,   31,   80,  122,   85,  132,   97,  132,  127,
          128,  129,  130,  131,   92,   97,   97,  128,  163,  134,
          135,   74,   70,   94,    1,  133,  100,   80,  100,  154,
          146,  137,  150,  160,   87,   88,   89,  113,   91,    1,
           93,  159,   95,  161,  162,   98,  161,  161,    1,  153,
          103,  104,  105,   74,   -1,   84,  109,  110,   -1,   80,
            1,   -1,  115,  116,   -1,   -1,   87,   88,   89,   31,
           91,  124,   93,  159,   95,  161,  138,   98,   31,  146,
           -1,  102,  103,  104,  105,   74,  134,  135,  109,  110,
          149,   80,   81,  149,  115,  116,  153,  149,   87,   88,
           89,  149,   91,  124,   93,  149,   95,   84,  149,   98,
          155,  158,  161,  161,  103,  104,  105,  159,  161,  159,
          109,  110,   84,  100,  101,  102,  115,  116,  159,  106,
          159,   84,  161,  159,  159,  124,  159,  159,  162,  159,
          117,  118,  159,   84,  159,  122,  159,  159,  159,  159,
          127,  128,  129,  130,  131,   70,  159,  159,  159,  100,
          101,  102,    1,  159,  161,  106,  160,   82,  160,  160,
          160,   86,  160,  166,  161,  161,  117,  118,  161,    1,
          161,  122,  159,  161,  161,  162,  127,  128,  129,  130,
          131,  161,   31,  161,  161,  161,  165,  159,  162,  161,
          163,  116,  162,  162,  162,  162,  159,  122,  161,  163,
          162,  162,  162,  162,  162,  162,  162,  162,  159,  162,
          161,  162,  137,  162,  139,  140,  141,  142,  143,  144,
          162,  162,  162,  162,  162,  162,  151,  152,  162,  162,
           74,  162,  162,   82,  162,  164,   80,  163,  163,  163,
          163,  166,  167,   87,   88,   89,  163,   91,  163,   93,
           82,   95,  163,  163,   98,  163,  163,  163,  163,  103,
          104,  105,  163,  163,  163,  109,  110,  116,  117,  118,
          163,  115,  116,  122,  163,  163,  163,  163,  163,  128,
          124,  163,  163,  163,  116,  117,  118,  163,  137,  163,
          122,  163,  163,  163,  163,  163,  128,  163,  163,  163,
          163,  163,  163,   -1,  164,  137,  164,  164,  164,  158,
          164,  164,  164,  164,  163,  165,  164,  164,  167,  164,
           -1,  164,  164,  164,  164,  164,  158,  164,  164,  164,
          164,  163,   -1,  164,  164,  167,  164,  164,  164,  164,
          164,  164,  164,  164,  164,  164,  164,  164,  164,  164,
          164,  164,  164,  164,  164,  164,  164,   -1,   -1,   -1,
          167
    );

    protected $actionBase = array(
            0,   -2,  154,  542,  752,  893,  929,   52,  374,  431,
          398,  869,  793,  235,  307,  307,  793,  307,  784,  908,
          908,  917,  908,  538,  841,  468,  468,  468,  708,  708,
          708,  708,  740,  740,  849,  849,  881,  817,  634, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036,  348,  346,  370,  653, 1063, 1069,
         1065, 1070, 1061, 1060, 1064, 1066, 1071,  946,  947,  774,
          949,  950,  943,  952, 1067,  882, 1062, 1068,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  525,  191,  359,    4,    4,
            4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,    4,    4,    4,    4,    4,  174,  174,
          174,  620,  620,   51,  465,  356,  955,  955,  955,  955,
          955,  955,  955,  955,  955,  955,  658,  184,  144,  144,
            7,    7,    7,    7,    7, 1031,  371, 1048,  -25,  -25,
          -25,  -25,   50,  725,  526,  449,   39,  317,   80,  474,
          474,   13,   13,  512,  512,  422,  422,  512,  512,  512,
          808,  808,  808,  808,  443,  505,  360,  308,  -78,  209,
          209,  209,  209,  -78,  -78,  -78,  -78,  803,  877,  -78,
          -78,  -78,   63,  641,  641,  822,   -1,   -1,   -1,  641,
          253,  790,  548,  253,  384,  548,  480,  402,  764,  759,
          -49,  447,  764,  639,  755,  198,  143,  825,  609,  825,
         1059,  320,  768,  426,  749,  720,  874,  904, 1072,  796,
          941,  798,  942,  106,  -58,  710, 1058, 1058, 1058, 1058,
         1058, 1058, 1058, 1058, 1058, 1058, 1058, 1073,  336, 1059,
          423, 1073, 1073, 1073,  336,  336,  336,  336,  336,  336,
          336,  336,  336,  336,  619,  423,  586,  616,  423,  795,
          336,  814,  348,  348,  348,  348,  348,  348,  348,  348,
          348,  348,  750,  202,  348,  348,  346,   78,   78,  348,
          484,   65,   78,   78,   78,   78,  348,  348,  348,  348,
          609,  783,  766,  613,  813,  492,  783,  783,  783,  473,
          135,  378,  488,  713,  775,   67,  779,  779,  785,  969,
          969,  779,  769,  779,  785,  975,  779,  779,  969,  969,
          823,  280,  563,  478,  550,  568,  969,  377,  779,  779,
          779,  779,  746,  573,  779,  342,  314,  779,  779,  746,
          744,  760,   43,  762,  969,  969,  969,  746,  547,  762,
          762,  762,  839,  844,  794,  758,  444,  433,  588,  232,
          801,  758,  758,  779,  558,  794,  758,  794,  758,  745,
          758,  758,  758,  794,  758,  769,  502,  758,  717,  583,
          224,  758,    6,  979,  980,  624,  981,  973,  987, 1019,
          991,  992,  873,  965,  999,  974,  993,  972,  970,  773,
          682,  684,  818,  811,  963,  777,  777,  777,  956,  777,
          777,  777,  777,  777,  777,  777,  777,  682,  743,  829,
          765, 1006,  689,  691,  754,  911,  901, 1030, 1004, 1049,
          994,  828,  694, 1028, 1008,  910,  821, 1009, 1010, 1029,
         1050, 1052,  912,  782,  913,  918,  876, 1012,  883,  777,
          979,  992,  693,  974,  993,  972,  970,  748,  739,  737,
          738,  736,  735,  723,  734,  753, 1053,  954,  907,  878,
         1011,  957,  682,  879, 1023,  756, 1032, 1033,  827,  788,
          778,  880,  919, 1014, 1015, 1016,  884, 1054,  887,  830,
         1024,  951, 1035,  789,  846, 1037, 1038, 1039, 1040,  889,
          920,  892,  916,  900,  845,  776, 1020,  761,  921,  591,
          787,  791,  800, 1018,  606, 1000,  902,  906,  922, 1041,
         1043, 1044,  923,  924,  995,  847, 1026,  799, 1027, 1022,
          848,  850,  617,  797, 1055,  781,  786,  772,  621,  632,
          925,  927,  931,  998,  763,  770,  853,  855, 1056,  771,
         1057,  938,  635,  857,  718,  939, 1046,  719,  724,  637,
          678,  672,  731,  792,  903,  826,  757,  780, 1017,  724,
          767,  858,  940,  859,  860,  867, 1045,  868,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,  458,  458,
          458,  458,  458,  458,  307,  307,  307,  307,  307,  307,
          307,    0,    0,  307,    0,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,   66,   66,
          291,  291,  291,   66,   66,   66,   66,   66,   66,   66,
           66,   66,   66,    0,  291,  291,  291,  291,  291,  291,
          291,  291,   66,  823,   66,   -1,   -1,   -1,   -1,   66,
           66,   66,  -88,  -88,   66,  384,   66,   66,   -1,   -1,
           66,   66,   66,   66,   66,   66,   66,   66,   66,   66,
           66,    0,    0,  423,  548,   66,  769,  769,  769,  769,
           66,   66,   66,   66,  548,  548,   66,   66,   66,    0,
            0,    0,    0,    0,    0,    0,    0,  423,  548,    0,
          423,    0,    0,  769,  769,   66,  384,  823,  643,   66,
            0,    0,    0,    0,  423,  769,  423,  336,  779,  548,
          779,  336,  336,   78,  348,  643,  611,  611,  611,  611,
            0,    0,  609,  823,  823,  823,  823,  823,  823,  823,
          823,  823,  823,  823,  769,    0,  823,    0,  769,  769,
          769,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,  769,    0,  969,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,  975,
            0,    0,    0,    0,    0,    0,    0,    0,  769,    0,
            0,    0,    0,    0,    0,    0,    0,    0,  777,  788,
            0,  788,    0,  777,  777,  777,    0,    0,    0,    0,
          797,  771
    );

    protected $actionDefault = array(
            3,32767,  103,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  101,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  596,  596,
          596,  596,32767,32767,  254,  103,32767,32767,  469,  387,
          387,  387,32767,32767,  540,  540,  540,  540,  540,  540,
        32767,32767,32767,32767,32767,32767,  469,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  101,
        32767,32767,32767,   37,    7,    8,   10,   11,   50,   17,
          324,32767,32767,32767,32767,  103,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  589,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  473,  452,
          453,  455,  456,  386,  541,  595,  327,  592,  385,  146,
          339,  329,  242,  330,  258,  474,  259,  475,  478,  479,
          215,  287,  382,  150,  151,  416,  470,  418,  468,  472,
          417,  392,  397,  398,  399,  400,  401,  402,  403,  404,
          405,  406,  407,  408,  409,  390,  391,  471,  449,  448,
          447,32767,32767,  414,  415,  419,32767,32767,32767,32767,
        32767,32767,32767,32767,  103,32767,  389,  422,  420,  421,
          438,  439,  436,  437,  440,32767,32767,32767,  441,  442,
          443,  444,  316,32767,32767,  366,  364,  316,  112,32767,
        32767,  429,  430,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  534,  446,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  103,
        32767,  101,  536,  411,  413,  503,  424,  425,  423,  393,
        32767,  510,32767,  103,32767,  512,32767,32767,32767,32767,
        32767,32767,32767,  535,32767,  542,  542,32767,  496,  101,
          195,32767,32767,32767,  195,  195,32767,32767,32767,32767,
        32767,32767,32767,32767,  603,  496,  111,  111,  111,  111,
          111,  111,  111,  111,  111,  111,  111,32767,  195,  111,
        32767,32767,32767,  101,  195,  195,  195,  195,  195,  195,
          195,  195,  195,  195,  190,32767,  268,  270,  103,  557,
          195,  515,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  508,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          496,  434,  139,32767,  139,  542,  426,  427,  428,  498,
          542,  542,  542,  312,  289,32767,32767,32767,32767,  513,
          513,  101,  101,  101,  101,  508,32767,32767,32767,32767,
          112,  100,  100,  100,  100,  100,  104,  102,32767,32767,
        32767,32767,  223,  100,32767,  102,  102,32767,32767,  223,
          225,  212,  102,  227,32767,  561,  562,  223,  102,  227,
          227,  227,  247,  247,  485,  318,  102,  100,  102,  102,
          197,  318,  318,32767,  102,  485,  318,  485,  318,  199,
          318,  318,  318,  485,  318,32767,  102,  318,  214,  100,
          100,  318,32767,32767,32767,  498,32767,32767,32767,32767,
        32767,32767,32767,  222,32767,32767,32767,32767,32767,32767,
        32767,  529,32767,  546,  559,  432,  433,  435,  544,  457,
          458,  459,  460,  461,  462,  463,  465,  591,32767,  502,
        32767,32767,32767,  338,  601,32767,  601,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  602,32767,  542,32767,32767,32767,32767,  431,
            9,   76,  491,   43,   44,   52,   58,  519,  520,  521,
          522,  516,  517,  523,  518,32767,32767,  524,  567,32767,
        32767,  543,  594,32767,32767,32767,32767,32767,32767,  139,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,  529,32767,  137,32767,32767,32767,32767,32767,32767,
        32767,32767,  525,32767,32767,32767,  542,32767,32767,32767,
        32767,  314,  311,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  542,
        32767,32767,32767,32767,32767,  291,32767,  308,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  286,32767,32767,  381,  498,  294,
          296,  297,32767,32767,32767,32767,  360,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  153,  153,
            3,    3,  341,  153,  153,  153,  341,  341,  153,  341,
          341,  341,  153,  153,  153,  153,  153,  153,  280,  185,
          262,  265,  247,  247,  153,  352,  153
    );

    protected $goto = array(
          196,  196, 1032,  704,  695,  431,  659, 1063, 1335, 1335,
          425,  313,  314,  335,  574,  319,  430,  336,  432,  636,
          652,  653,  851,  670,  671,  672, 1335,  167,  167,  167,
          167,  221,  197,  193,  193,  177,  179,  216,  193,  193,
          193,  193,  193,  194,  194,  194,  194,  194,  194,  188,
          189,  190,  191,  192,  218,  216,  219,  535,  536,  421,
          537,  539,  540,  541,  542,  543,  544,  545,  546, 1133,
          168,  169,  170,  195,  171,  172,  173,  166,  174,  175,
          176,  178,  215,  217,  220,  238,  243,  244,  245,  257,
          258,  259,  260,  261,  262,  263,  264,  268,  269,  270,
          271,  281,  282,  316,  317,  318,  426,  427,  428,  579,
          222,  223,  224,  225,  226,  227,  228,  229,  230,  231,
          232,  233,  234,  235,  236,  180,  237,  181,  198,  199,
          200,  239,  188,  189,  190,  191,  192,  218, 1133,  201,
          182,  183,  184,  202,  198,  185,  240,  203,  201,  165,
          204,  205,  186,  206,  207,  208,  187,  209,  210,  211,
          212,  213,  214,  854,  619,  656,  278,  278,  278,  278,
          852,  621,  621,  350,  419,  598, 1266, 1266, 1266, 1266,
         1266, 1266, 1266, 1266, 1266, 1266, 1284, 1284,  832, 1105,
         1106, 1284, 1284, 1284, 1284, 1284, 1284, 1284, 1284, 1284,
         1284,  353,  353,  353,  353,  826,  558,  551,  908,  903,
          904,  917,  860,  905,  857,  906,  907,  858, 1035, 1035,
          911,  864,  679,  950,  458,  863, 1027, 1043, 1044,  832,
          885,  832, 1085, 1080, 1081, 1082,  341,  551,  558,  567,
          568,  343,  577,  600,  614,  615,  547,  547,  547,  547,
          973,  602,   15,  394,  397,  559,  599,  603, 1213,  942,
         1234,  571, 1234, 1214, 1217,  943, 1218, 1032, 1032, 1234,
          440,  912, 1032,  913, 1032, 1032, 1038, 1037, 1032, 1032,
         1032, 1032, 1032, 1032, 1032, 1032, 1032, 1032, 1032, 1316,
         1316, 1316, 1316, 1234,  476, 1309, 1310,  348, 1234, 1234,
         1234, 1234,  407,  408, 1234, 1234, 1234,  668, 1324,  669,
          354,  412,  413,  414,  867,  682,  466,  466,  415,  994,
          354,  354,  346,  924,  424,  466,  609,  925,    5,  879,
            6,  940,  866,  940,  354,  354, 1282, 1282,  354,  392,
         1351, 1282, 1282, 1282, 1282, 1282, 1282, 1282, 1282, 1282,
         1282,  553,  538,  538,  570,  354,  658,  538,  538,  538,
          538,  538,  538,  538,  538,  538,  538,  549,  565,  549,
         1041, 1042,  731,  635,  637,  850,  549,  657,  965,  410,
          703,  681,  685, 1008,  693,  702, 1004,  252,  252,  997,
          971,  971,  969,  971,  730, 1056,  550, 1006, 1001, 1182,
          456,  847, 1295, 1334, 1334,  967,  967,  967,  967,  325,
          308,  456,  961,  968,  249,  249,  249,  249,  251,  253,
          438, 1334,  351,  352,  684,  680,  552,  562,  450,  450,
          450,  552, 1306,  562, 1306,  479,  395,  462, 1337, 1311,
         1312, 1306,  664,  481,  500,  337,  501,  844,  469,  578,
          470,  471,  507,  847,  877,  553,  872, 1342, 1343, 1011,
         1011,  575,  612,  324,  275,  324, 1318, 1318, 1318, 1318,
          607,  622,  625,  626,  627,  628,  649,  650,  651,  706,
          956,  401,  692,  875, 1229,  828,  869,  692,  629,  631,
          633,  692,  433, 1302, 1225,  734,  613,  433,  880,  868,
         1068, 1072, 1069, 1016,  477, 1039, 1039,  881,    0,  976,
          663, 1050, 1046, 1047, 1073, 1116,  978,    0, 1227,  450,
          450,  450,  450,  450,  450,  450,  450,  450,  450,  450,
          929, 1121,  450,  966, 1071,    0,    0,  617, 1304, 1304,
         1071, 1230, 1231, 1013,    0,    0,    0,    0,    0,  842,
            0,  871,    0,  662,  992, 1114,  884,  597, 1098,  865,
          707,    0,    0,  508,  698,    0, 1096, 1232, 1292, 1293,
            0, 1224,    0,  847,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,  255,  255
    );

    protected $gotoCheck = array(
           42,   42,   72,    9,   72,   65,   65,  126,  181,  181,
           65,   65,   65,   65,   65,   65,   65,   65,   65,   65,
           85,   85,   26,   85,   85,   85,  181,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   15,   55,   55,   23,   23,   23,   23,
           27,  107,  107,   96,   43,  129,  107,  107,  107,  107,
          107,  107,  107,  107,  107,  107,  168,  168,   12,  143,
          143,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,   24,   24,   24,   24,    6,   75,   75,   15,   15,
           15,   15,   15,   15,   15,   15,   15,   15,   88,   88,
           15,   15,   88,   88,   82,   15,   88,   88,   88,   12,
           45,   12,   15,   15,   15,   15,   75,   75,   75,   75,
           75,   75,   75,   75,   75,   75,  106,  106,  106,  106,
           49,  106,   75,   58,   58,   58,   58,   58,   78,   78,
           72,  170,   72,   78,   78,   78,   78,   72,   72,   72,
           82,   64,   72,   64,   72,   72,  117,  117,   72,   72,
           72,   72,   72,   72,   72,   72,   72,   72,   72,    9,
            9,    9,    9,   72,  174,  174,  174,  177,   72,   72,
           72,   72,   81,   81,   72,   72,   72,   81,  179,   81,
           14,   81,   81,   81,   35,   81,  148,  148,   81,  102,
           14,   14,   81,   72,   13,  148,   13,   72,   46,   35,
           46,    9,   35,    9,   14,   14,  169,  169,   14,   61,
           14,  169,  169,  169,  169,  169,  169,  169,  169,  169,
          169,   14,  171,  171,  103,   14,   63,  171,  171,  171,
          171,  171,  171,  171,  171,  171,  171,   19,   48,   19,
          118,  118,   48,   48,   48,   25,   19,   48,   92,   92,
           92,   48,   48,   48,   48,   48,   48,    5,    5,   25,
           25,   25,   25,   25,   25,  113,   25,   25,   25,  150,
           19,   22,   14,  180,  180,   19,   19,   19,   19,  167,
          167,   19,   19,   19,    5,    5,    5,    5,    5,    5,
          112,  180,   96,   96,   14,  115,    9,    9,   23,   23,
           23,    9,  129,    9,  129,   83,    9,    9,  180,  176,
          176,  129,  119,   83,  154,   29,  154,   18,    9,    9,
            9,    9,  154,   22,    9,   14,   39,    9,    9,  106,
          106,    2,    2,   24,   24,   24,  129,  129,  129,  129,
           80,   80,   80,   80,   80,   80,   80,   80,   80,   80,
           91,   28,    7,    9,   20,    7,   37,    7,   84,   84,
           84,    7,  116,  129,  159,   98,   79,  116,   16,   16,
           16,   16,  128,  109,  156,  116,  116,   41,   -1,   16,
          116,  116,  116,  116,  131,  146,   95,   -1,   14,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           17,   17,   23,   16,  129,   -1,   -1,   17,  129,  129,
          129,   20,   20,   17,   -1,   -1,   -1,   -1,   -1,   20,
           -1,   17,   -1,   17,   17,   16,   16,    8,    8,   17,
            8,   -1,   -1,    8,    8,   -1,    8,   20,   20,   20,
           -1,   17,   -1,   22,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,    5,    5
    );

    protected $gotoBase = array(
            0,    0, -249,    0,    0,  386,  192,  475,  549,  -10,
            0,    0, -108,  -13,   13, -184,   46,   65,  138,  102,
           93,    0,  123,  163,  198,  371,   18,  166,  144,  149,
            0,    0,    0,    0,    0,  -56,    0,  147,    0,  133,
            0,   66,   -1,  162,    0,  214, -406,    0, -341,  226,
            0,    0,    0,    0,    0,  124,    0,    0,  208,    0,
            0,  297,    0,  114,  251, -236,    0,    0,    0,    0,
            0,    0,   -5,    0,    0, -138,    0,    0, -149,  153,
          113, -189,  -54,  -34,    9, -696,    0,    0,  -61,    0,
            0,  151,   74,    0,    0,   73, -310,    0,   89,    0,
            0,    0,  284,  311,    0,    0,  218,  -70,    0,  134,
            0,    0,  143,  122,    0,  142,  220,   -3,   85,  152,
            0,    0,    0,    0,    0,    0,    5,    0,  129,  167,
            0,   72,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,  -86,    0,    0,   71,    0,  282,    0,
          125,    0,    0,    0,  -51,    0,   64,    0,    0,  126,
            0,    0,    0,    0,    0,    0,    0,   88,  -55,   95,
          232,  111,    0,    0,   -6,    0,   68,  267,    0,  277,
           96, -299,    0,    0
    );

    protected $gotoDefault = array(
        -32768,  512,  738,    4,  739,  933,  815,  824,  595,  529,
          705,  347,  623,  422, 1300,  910, 1120,  576,  843, 1243,
         1251,  457,  846,  330,  728,  892,  893,  894,  398,  385,
          859,  396,  647,  624,  494,  878,  453,  870,  486,  873,
          452,  882,  164,  418,  510,  886,    3,  889,  555,  920,
          386,  897,  387,  675,  899,  561,  901,  902,  393,  399,
          400, 1125,  569,  620,  914,  256,  563,  915,  384,  916,
          923,  389,  391,  686,  465,  505,  499,  411, 1100,  564,
          606,  644,  447,  473,  618,  630,  616,  480,  434,  416,
          329,  955,  963,  487,  463,  977,  349,  985,  736, 1132,
          638,  489,  993,  639, 1000, 1003,  530,  531,  478, 1015,
          272, 1018,  490,   12,  665, 1029, 1030,  666,  640, 1052,
          641,  667,  642, 1054,  472,  596, 1062,  454, 1070, 1288,
          455, 1074,  266, 1077,  277,  417,  435, 1083, 1084,    9,
         1090,  696,  697,   11,  276,  509, 1115,  687,  451, 1131,
          439, 1201, 1203,  557,  491, 1221, 1220,  678,  506, 1226,
          448, 1291,  449,  532,  474,  315,  533,  307,  333,  312,
          548,  294,  334,  534,  475, 1297, 1305,  331,   31, 1325,
         1336,  342,  573,  611
    );

    protected $ruleToNonTerminal = array(
            0,    1,    3,    3,    2,    5,    5,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    7,    7,
            7,    7,    7,    7,    7,    7,    8,    8,    9,   10,
           11,   11,   11,   12,   12,   13,   13,   14,   15,   15,
           16,   16,   17,   17,   18,   18,   21,   21,   22,   23,
           23,   24,   24,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,    4,   29,   29,   30,   30,   32,   34,
           34,   28,   36,   36,   33,   38,   38,   35,   35,   37,
           37,   39,   39,   31,   40,   40,   41,   43,   44,   44,
           45,   45,   46,   46,   48,   47,   47,   47,   47,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   25,   25,   68,   68,   71,   71,   70,
           69,   69,   62,   74,   74,   75,   75,   76,   76,   77,
           77,   78,   78,   79,   79,   26,   26,   27,   27,   27,
           27,   27,   87,   87,   89,   89,   82,   82,   90,   90,
           91,   91,   91,   83,   83,   86,   86,   84,   84,   92,
           93,   93,   56,   56,   64,   64,   67,   67,   67,   66,
           94,   94,   95,   57,   57,   57,   57,   96,   96,   97,
           97,   98,   98,   99,  100,  100,  101,  101,  102,  102,
           54,   54,   50,   50,  104,   52,   52,  105,   51,   51,
           53,   53,   63,   63,   63,   63,   80,   80,  108,  108,
          110,  110,  111,  111,  111,  111,  109,  109,  109,  113,
          113,  113,  113,   88,   88,  116,  116,  116,  117,  117,
          114,  114,  118,  118,  120,  120,  121,  121,  115,  122,
          122,  119,  123,  123,  123,  123,  112,  112,   81,   81,
           81,   20,   20,   20,  125,  124,  124,  126,  126,  126,
          126,   59,  127,  127,  128,   60,  130,  130,  131,  131,
          132,  132,   85,  133,  133,  133,  133,  133,  133,  133,
          138,  138,  139,  139,  140,  140,  140,  140,  140,  141,
          142,  142,  137,  137,  134,  134,  136,  136,  144,  144,
          143,  143,  143,  143,  143,  143,  143,  135,  145,  145,
          147,  146,  146,   61,  103,  148,  148,   55,   55,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,  155,  149,  149,  154,  154,  157,  158,  158,
          159,  160,  161,  161,  161,  161,   19,   19,   72,   72,
           72,   72,  150,  150,  150,  150,  163,  163,  151,  151,
          153,  153,  153,  156,  156,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  169,  169,  169,  107,  171,  171,
          171,  171,  152,  152,  152,  152,  152,  152,  152,  152,
           58,   58,  166,  166,  166,  166,  172,  172,  162,  162,
          162,  173,  173,  173,  173,  173,  173,   73,   73,   65,
           65,   65,   65,  129,  129,  129,  129,  176,  175,  165,
          165,  165,  165,  165,  165,  165,  164,  164,  164,  174,
          174,  174,  174,  106,  170,  178,  178,  177,  177,  179,
          179,  179,  179,  179,  179,  179,  179,  167,  167,  167,
          167,  181,  182,  180,  180,  180,  180,  180,  180,  180,
          180,  183,  183,  183,  183
    );

    protected $ruleToLength = array(
            1,    1,    2,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            0,    1,    0,    1,    1,    2,    1,    3,    4,    1,
            2,    0,    1,    1,    1,    1,    1,    3,    5,    4,
            3,    4,    1,    3,    1,    1,    8,    7,    2,    3,
            1,    2,    3,    1,    2,    3,    1,    1,    3,    1,
            3,    1,    2,    2,    3,    1,    3,    2,    3,    1,
            3,    3,    2,    0,    1,    1,    1,    1,    1,    3,
            7,   10,    5,    7,    9,    5,    3,    3,    3,    3,
            3,    3,    1,    2,    5,    7,    9,    6,    5,    6,
            3,    2,    1,    1,    1,    0,    2,    1,    3,    8,
            0,    4,    2,    1,    3,    0,    1,    0,    1,    0,
            1,    3,    1,    1,    1,    8,    9,    7,    8,    7,
            6,    8,    0,    2,    0,    2,    1,    2,    1,    2,
            1,    1,    1,    0,    2,    0,    2,    0,    2,    2,
            1,    3,    1,    4,    1,    4,    1,    1,    4,    2,
            1,    3,    3,    3,    4,    4,    5,    0,    2,    4,
            3,    1,    1,    7,    0,    2,    1,    3,    3,    4,
            1,    4,    0,    2,    5,    0,    2,    6,    0,    2,
            0,    3,    1,    2,    1,    1,    2,    0,    1,    3,
            0,    2,    1,    1,    1,    1,    6,    8,    6,    1,
            2,    1,    1,    1,    1,    1,    1,    1,    1,    3,
            3,    3,    1,    3,    3,    3,    3,    3,    1,    3,
            3,    1,    1,    2,    1,    1,    0,    1,    0,    2,
            2,    2,    4,    3,    1,    1,    3,    1,    2,    2,
            3,    2,    3,    1,    1,    2,    3,    1,    1,    3,
            2,    0,    1,    5,    5,    6,   10,    3,    5,    1,
            1,    3,    0,    2,    4,    5,    4,    4,    4,    3,
            1,    1,    1,    1,    1,    1,    0,    1,    1,    2,
            1,    1,    1,    1,    1,    1,    1,    2,    1,    3,
            1,    1,    3,    2,    2,    3,    1,    0,    1,    1,
            3,    3,    3,    4,    1,    1,    2,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            2,    2,    2,    2,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    2,    2,    2,    2,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    5,    4,    3,
            4,    4,    2,    2,    4,    2,    2,    2,    2,    2,
            2,    2,    2,    2,    2,    2,    1,    3,    2,    1,
            2,    4,    2,    2,    8,    9,    8,    9,    9,   10,
            9,   10,    8,    3,    2,    0,    4,    2,    1,    3,
            2,    1,    2,    2,    2,    4,    1,    1,    1,    1,
            1,    1,    1,    1,    3,    1,    1,    1,    0,    3,
            0,    1,    1,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    3,    5,    3,    3,    4,    1,
            1,    3,    1,    1,    1,    1,    1,    3,    2,    3,
            0,    1,    1,    3,    1,    1,    1,    1,    1,    3,
            1,    1,    4,    4,    1,    4,    4,    0,    1,    1,
            1,    3,    3,    1,    4,    2,    2,    1,    3,    1,
            4,    4,    3,    3,    3,    3,    1,    3,    1,    1,
            3,    1,    1,    4,    1,    1,    1,    3,    1,    1,
            2,    1,    3,    4,    3,    2,    0,    2,    2,    1,
            2,    1,    1,    1,    4,    3,    3,    3,    3,    6,
            3,    1,    1,    2,    1
    );

    protected function initReduceCallbacks() {
        $this->reduceCallbacks = [
            0 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            1 => function ($stackPos) {
                 $this->semValue = $this->handleNamespaces($this->semStack[$stackPos-(1-1)]);
            },
            2 => function ($stackPos) {
                 if (is_array($this->semStack[$stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]); } else { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; };
            },
            3 => function ($stackPos) {
                 $this->semValue = array();
            },
            4 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            5 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            6 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            7 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            8 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            9 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            10 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            11 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            12 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            13 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            14 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            15 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            16 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            17 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            18 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            19 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            20 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            21 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            22 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            23 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            24 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            25 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            26 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            27 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            28 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            29 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            30 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            31 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            32 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            33 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            34 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            35 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            36 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            37 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            38 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            39 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            40 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            41 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            42 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            43 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            44 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            45 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            46 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            47 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            48 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            49 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            50 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            51 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            52 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            53 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            54 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            55 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            56 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            57 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            58 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            59 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            60 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            61 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            62 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            63 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            64 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            65 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            66 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            67 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            68 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            69 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            70 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            71 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            72 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            73 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            74 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            75 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            76 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            77 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            78 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            79 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            80 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            81 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            82 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            83 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            84 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            85 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            86 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            87 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            88 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            89 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            90 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            91 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            92 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            93 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            94 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            95 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            96 => function ($stackPos) {
                 $this->semValue = new Name(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            97 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            98 => function ($stackPos) {
                 /* nothing */
            },
            99 => function ($stackPos) {
                 /* nothing */
            },
            100 => function ($stackPos) {
                 /* nothing */
            },
            101 => function ($stackPos) {
                 $this->emitError(new Error('A trailing comma is not allowed here', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes));
            },
            102 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            103 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            104 => function ($stackPos) {
                 $this->semValue = new Node\Attribute($this->semStack[$stackPos-(1-1)], [], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            105 => function ($stackPos) {
                 $this->semValue = new Node\Attribute($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            106 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            107 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            108 => function ($stackPos) {
                 $this->semValue = new Node\AttributeGroup($this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            109 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            110 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            111 => function ($stackPos) {
                 $this->semValue = [];
            },
            112 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            113 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            114 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            115 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            116 => function ($stackPos) {
                 $this->semValue = new Stmt\HaltCompiler($this->lexer->handleHaltCompiler(), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            117 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(3-2)], null, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_SEMICOLON);
            $this->checkNamespace($this->semValue);
            },
            118 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            119 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_(null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            120 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(3-2)], Stmt\Use_::TYPE_NORMAL, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            121 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            122 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            123 => function ($stackPos) {
                 $this->semValue = new Stmt\Const_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            124 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_FUNCTION;
            },
            125 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_CONSTANT;
            },
            126 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-6)], $this->semStack[$stackPos-(8-2)], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            127 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(7-2)], $this->semStack[$stackPos-(7-5)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            128 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            129 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            130 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            131 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            132 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            133 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            134 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            135 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            136 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            137 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            138 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            139 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            140 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            141 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $this->semValue->type = Stmt\Use_::TYPE_NORMAL;
            },
            142 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)]; $this->semValue->type = $this->semStack[$stackPos-(2-1)];
            },
            143 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            144 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            145 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            146 => function ($stackPos) {
                 $this->semValue = new Node\Const_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            147 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            148 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            149 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            150 => function ($stackPos) {
                 $this->semValue = new Node\Const_(new Node\Identifier($this->semStack[$stackPos-(3-1)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributeStack[$stackPos-(3-1)]), $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            151 => function ($stackPos) {
                 $this->semValue = new Node\Const_(new Node\Identifier($this->semStack[$stackPos-(3-1)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributeStack[$stackPos-(3-1)]), $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            152 => function ($stackPos) {
                 if (is_array($this->semStack[$stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]); } else { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; };
            },
            153 => function ($stackPos) {
                 $this->semValue = array();
            },
            154 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            155 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            156 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            157 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            158 => function ($stackPos) {
                 throw new Error('__HALT_COMPILER() can only be used from the outermost scope', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            159 => function ($stackPos) {

        if ($this->semStack[$stackPos-(3-2)]) {
            $this->semValue = $this->semStack[$stackPos-(3-2)]; $attrs = $this->startAttributeStack[$stackPos-(3-1)]; $stmts = $this->semValue; if (!empty($attrs['comments'])) {$stmts[0]->setAttribute('comments', array_merge($attrs['comments'], $stmts[0]->getAttribute('comments', []))); };
        } else {
            $startAttributes = $this->startAttributeStack[$stackPos-(3-1)]; if (isset($startAttributes['comments'])) { $this->semValue = new Stmt\Nop($startAttributes + $this->endAttributes); } else { $this->semValue = null; };
            if (null === $this->semValue) { $this->semValue = array(); }
        }

            },
            160 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(7-3)], ['stmts' => is_array($this->semStack[$stackPos-(7-5)]) ? $this->semStack[$stackPos-(7-5)] : array($this->semStack[$stackPos-(7-5)]), 'elseifs' => $this->semStack[$stackPos-(7-6)], 'else' => $this->semStack[$stackPos-(7-7)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            161 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(10-3)], ['stmts' => $this->semStack[$stackPos-(10-6)], 'elseifs' => $this->semStack[$stackPos-(10-7)], 'else' => $this->semStack[$stackPos-(10-8)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            162 => function ($stackPos) {
                 $this->semValue = new Stmt\While_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            163 => function ($stackPos) {
                 $this->semValue = new Stmt\Do_($this->semStack[$stackPos-(7-5)], is_array($this->semStack[$stackPos-(7-2)]) ? $this->semStack[$stackPos-(7-2)] : array($this->semStack[$stackPos-(7-2)]), $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            164 => function ($stackPos) {
                 $this->semValue = new Stmt\For_(['init' => $this->semStack[$stackPos-(9-3)], 'cond' => $this->semStack[$stackPos-(9-5)], 'loop' => $this->semStack[$stackPos-(9-7)], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            165 => function ($stackPos) {
                 $this->semValue = new Stmt\Switch_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            166 => function ($stackPos) {
                 $this->semValue = new Stmt\Break_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            167 => function ($stackPos) {
                 $this->semValue = new Stmt\Continue_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            168 => function ($stackPos) {
                 $this->semValue = new Stmt\Return_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            169 => function ($stackPos) {
                 $this->semValue = new Stmt\Global_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            170 => function ($stackPos) {
                 $this->semValue = new Stmt\Static_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            171 => function ($stackPos) {
                 $this->semValue = new Stmt\Echo_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            172 => function ($stackPos) {
                 $this->semValue = new Stmt\InlineHTML($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            173 => function ($stackPos) {

        $e = $this->semStack[$stackPos-(2-1)];
        if ($e instanceof Expr\Throw_) {
            // For backwards-compatibility reasons, convert throw in statement position into
            // Stmt\Throw_ rather than Stmt\Expression(Expr\Throw_).
            $this->semValue = new Stmt\Throw_($e->expr, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
        } else {
            $this->semValue = new Stmt\Expression($e, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
        }

            },
            174 => function ($stackPos) {
                 $this->semValue = new Stmt\Unset_($this->semStack[$stackPos-(5-3)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            175 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-5)][0], ['keyVar' => null, 'byRef' => $this->semStack[$stackPos-(7-5)][1], 'stmts' => $this->semStack[$stackPos-(7-7)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            176 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(9-3)], $this->semStack[$stackPos-(9-7)][0], ['keyVar' => $this->semStack[$stackPos-(9-5)], 'byRef' => $this->semStack[$stackPos-(9-7)][1], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            177 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(6-3)], new Expr\Error($this->startAttributeStack[$stackPos-(6-4)] + $this->endAttributeStack[$stackPos-(6-4)]), ['stmts' => $this->semStack[$stackPos-(6-6)]], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            178 => function ($stackPos) {
                 $this->semValue = new Stmt\Declare_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            179 => function ($stackPos) {
                 $this->semValue = new Stmt\TryCatch($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-5)], $this->semStack[$stackPos-(6-6)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes); $this->checkTryCatch($this->semValue);
            },
            180 => function ($stackPos) {
                 $this->semValue = new Stmt\Goto_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            181 => function ($stackPos) {
                 $this->semValue = new Stmt\Label($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            182 => function ($stackPos) {
                 $this->semValue = array(); /* means: no statement */
            },
            183 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            184 => function ($stackPos) {
                 $startAttributes = $this->startAttributeStack[$stackPos-(1-1)]; if (isset($startAttributes['comments'])) { $this->semValue = new Stmt\Nop($startAttributes + $this->endAttributes); } else { $this->semValue = null; };
            if ($this->semValue === null) $this->semValue = array(); /* means: no statement */
            },
            185 => function ($stackPos) {
                 $this->semValue = array();
            },
            186 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            187 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            188 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            189 => function ($stackPos) {
                 $this->semValue = new Stmt\Catch_($this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-7)], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            190 => function ($stackPos) {
                 $this->semValue = null;
            },
            191 => function ($stackPos) {
                 $this->semValue = new Stmt\Finally_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            192 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            193 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            194 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            195 => function ($stackPos) {
                 $this->semValue = false;
            },
            196 => function ($stackPos) {
                 $this->semValue = true;
            },
            197 => function ($stackPos) {
                 $this->semValue = false;
            },
            198 => function ($stackPos) {
                 $this->semValue = true;
            },
            199 => function ($stackPos) {
                 $this->semValue = false;
            },
            200 => function ($stackPos) {
                 $this->semValue = true;
            },
            201 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            202 => function ($stackPos) {
                 $this->semValue = [];
            },
            203 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            204 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            205 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(8-3)], ['byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-5)], 'returnType' => $this->semStack[$stackPos-(8-7)], 'stmts' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            206 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(9-4)], ['byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-6)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            207 => function ($stackPos) {
                 $this->semValue = new Stmt\Class_($this->semStack[$stackPos-(7-2)], ['type' => $this->semStack[$stackPos-(7-1)], 'extends' => $this->semStack[$stackPos-(7-3)], 'implements' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            $this->checkClass($this->semValue, $stackPos-(7-2));
            },
            208 => function ($stackPos) {
                 $this->semValue = new Stmt\Class_($this->semStack[$stackPos-(8-3)], ['type' => $this->semStack[$stackPos-(8-2)], 'extends' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            $this->checkClass($this->semValue, $stackPos-(8-3));
            },
            209 => function ($stackPos) {
                 $this->semValue = new Stmt\Interface_($this->semStack[$stackPos-(7-3)], ['extends' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)], 'attrGroups' => $this->semStack[$stackPos-(7-1)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            $this->checkInterface($this->semValue, $stackPos-(7-3));
            },
            210 => function ($stackPos) {
                 $this->semValue = new Stmt\Trait_($this->semStack[$stackPos-(6-3)], ['stmts' => $this->semStack[$stackPos-(6-5)], 'attrGroups' => $this->semStack[$stackPos-(6-1)]], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            211 => function ($stackPos) {
                 $this->semValue = new Stmt\Enum_($this->semStack[$stackPos-(8-3)], ['scalarType' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            $this->checkEnum($this->semValue, $stackPos-(8-3));
            },
            212 => function ($stackPos) {
                 $this->semValue = null;
            },
            213 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            214 => function ($stackPos) {
                 $this->semValue = null;
            },
            215 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            216 => function ($stackPos) {
                 $this->semValue = 0;
            },
            217 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            218 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            219 => function ($stackPos) {
                 $this->checkClassModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            220 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
            },
            221 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_FINAL;
            },
            222 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_READONLY;
            },
            223 => function ($stackPos) {
                 $this->semValue = null;
            },
            224 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            225 => function ($stackPos) {
                 $this->semValue = array();
            },
            226 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            227 => function ($stackPos) {
                 $this->semValue = array();
            },
            228 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            229 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            230 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            231 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            232 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            233 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            234 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            235 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            236 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            237 => function ($stackPos) {
                 $this->semValue = null;
            },
            238 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            239 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            240 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            241 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            242 => function ($stackPos) {
                 $this->semValue = new Stmt\DeclareDeclare($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            243 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            244 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            245 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            246 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(5-3)];
            },
            247 => function ($stackPos) {
                 $this->semValue = array();
            },
            248 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            249 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_($this->semStack[$stackPos-(4-2)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            250 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_(null, $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            251 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            252 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            253 => function ($stackPos) {
                 $this->semValue = new Expr\Match_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-6)], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            254 => function ($stackPos) {
                 $this->semValue = [];
            },
            255 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            256 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            257 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            258 => function ($stackPos) {
                 $this->semValue = new Node\MatchArm($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            259 => function ($stackPos) {
                 $this->semValue = new Node\MatchArm(null, $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            260 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            261 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            262 => function ($stackPos) {
                 $this->semValue = array();
            },
            263 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            264 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(5-3)], is_array($this->semStack[$stackPos-(5-5)]) ? $this->semStack[$stackPos-(5-5)] : array($this->semStack[$stackPos-(5-5)]), $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            265 => function ($stackPos) {
                 $this->semValue = array();
            },
            266 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            267 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-6)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes); $this->fixupAlternativeElse($this->semValue);
            },
            268 => function ($stackPos) {
                 $this->semValue = null;
            },
            269 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_(is_array($this->semStack[$stackPos-(2-2)]) ? $this->semStack[$stackPos-(2-2)] : array($this->semStack[$stackPos-(2-2)]), $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            270 => function ($stackPos) {
                 $this->semValue = null;
            },
            271 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_($this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->fixupAlternativeElse($this->semValue);
            },
            272 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            273 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-2)], true);
            },
            274 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            275 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            276 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            277 => function ($stackPos) {
                 $this->semValue = array();
            },
            278 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            279 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            280 => function ($stackPos) {
                 $this->semValue = 0;
            },
            281 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            282 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PUBLIC;
            },
            283 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PROTECTED;
            },
            284 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PRIVATE;
            },
            285 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_READONLY;
            },
            286 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(6-6)], null, $this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-5)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes, $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-1)]);
            $this->checkParam($this->semValue);
            },
            287 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(8-6)], $this->semStack[$stackPos-(8-8)], $this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-5)], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes, $this->semStack[$stackPos-(8-2)], $this->semStack[$stackPos-(8-1)]);
            $this->checkParam($this->semValue);
            },
            288 => function ($stackPos) {
                 $this->semValue = new Node\Param(new Expr\Error($this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes), null, $this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-5)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes, $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-1)]);
            },
            289 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            290 => function ($stackPos) {
                 $this->semValue = new Node\NullableType($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            291 => function ($stackPos) {
                 $this->semValue = new Node\UnionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            292 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            293 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            294 => function ($stackPos) {
                 $this->semValue = new Node\Name('static', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            295 => function ($stackPos) {
                 $this->semValue = $this->handleBuiltinTypes($this->semStack[$stackPos-(1-1)]);
            },
            296 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('array', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            297 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('callable', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            298 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            299 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            300 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            301 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            302 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            303 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            304 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            305 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            306 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            307 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            308 => function ($stackPos) {
                 $this->semValue = new Node\IntersectionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            309 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            310 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            311 => function ($stackPos) {
                 $this->semValue = new Node\IntersectionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            312 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            313 => function ($stackPos) {
                 $this->semValue = new Node\NullableType($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            314 => function ($stackPos) {
                 $this->semValue = new Node\UnionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            315 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            316 => function ($stackPos) {
                 $this->semValue = null;
            },
            317 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            318 => function ($stackPos) {
                 $this->semValue = null;
            },
            319 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            320 => function ($stackPos) {
                 $this->semValue = null;
            },
            321 => function ($stackPos) {
                 $this->semValue = array();
            },
            322 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            323 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-2)]);
            },
            324 => function ($stackPos) {
                 $this->semValue = new Node\VariadicPlaceholder($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            325 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            326 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            327 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(1-1)], false, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            328 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], true, false, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            329 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], false, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            330 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(3-3)], false, false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->semStack[$stackPos-(3-1)]);
            },
            331 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            332 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            333 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            334 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            335 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            336 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            337 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            338 => function ($stackPos) {
                 $this->semValue = new Stmt\StaticVar($this->semStack[$stackPos-(1-1)], null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            339 => function ($stackPos) {
                 $this->semValue = new Stmt\StaticVar($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            340 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(2-2)] !== null) { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; } else { $this->semValue = $this->semStack[$stackPos-(2-1)]; }
            },
            341 => function ($stackPos) {
                 $this->semValue = array();
            },
            342 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            343 => function ($stackPos) {
                 $this->semValue = new Stmt\Property($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes, $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-1)]);
            $this->checkProperty($this->semValue, $stackPos-(5-2));
            },
            344 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassConst($this->semStack[$stackPos-(5-4)], $this->semStack[$stackPos-(5-2)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes, $this->semStack[$stackPos-(5-1)]);
            $this->checkClassConst($this->semValue, $stackPos-(5-2));
            },
            345 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassConst($this->semStack[$stackPos-(6-5)], $this->semStack[$stackPos-(6-2)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes, $this->semStack[$stackPos-(6-1)], $this->semStack[$stackPos-(6-4)]);
            $this->checkClassConst($this->semValue, $stackPos-(6-2));
            },
            346 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassMethod($this->semStack[$stackPos-(10-5)], ['type' => $this->semStack[$stackPos-(10-2)], 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-7)], 'returnType' => $this->semStack[$stackPos-(10-9)], 'stmts' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            $this->checkClassMethod($this->semValue, $stackPos-(10-2));
            },
            347 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUse($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            348 => function ($stackPos) {
                 $this->semValue = new Stmt\EnumCase($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->semStack[$stackPos-(5-1)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            349 => function ($stackPos) {
                 $this->semValue = null; /* will be skipped */
            },
            350 => function ($stackPos) {
                 $this->semValue = array();
            },
            351 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            352 => function ($stackPos) {
                 $this->semValue = array();
            },
            353 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            354 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Precedence($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            355 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(5-1)][0], $this->semStack[$stackPos-(5-1)][1], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            356 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], null, $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            357 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            358 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            359 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            360 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            361 => function ($stackPos) {
                 $this->semValue = array(null, $this->semStack[$stackPos-(1-1)]);
            },
            362 => function ($stackPos) {
                 $this->semValue = null;
            },
            363 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            364 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            365 => function ($stackPos) {
                 $this->semValue = 0;
            },
            366 => function ($stackPos) {
                 $this->semValue = 0;
            },
            367 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            368 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            369 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            370 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PUBLIC;
            },
            371 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PROTECTED;
            },
            372 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PRIVATE;
            },
            373 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_STATIC;
            },
            374 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
            },
            375 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_FINAL;
            },
            376 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_READONLY;
            },
            377 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            378 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            379 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            380 => function ($stackPos) {
                 $this->semValue = new Node\VarLikeIdentifier(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            381 => function ($stackPos) {
                 $this->semValue = new Stmt\PropertyProperty($this->semStack[$stackPos-(1-1)], null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            382 => function ($stackPos) {
                 $this->semValue = new Stmt\PropertyProperty($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            383 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            384 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            385 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            386 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            387 => function ($stackPos) {
                 $this->semValue = array();
            },
            388 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            389 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            390 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            391 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            392 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            393 => function ($stackPos) {
                 $this->semValue = new Expr\AssignRef($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            394 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            395 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            396 => function ($stackPos) {
                 $this->semValue = new Expr\Clone_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            397 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            398 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            399 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            400 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            401 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            402 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            403 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            404 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            405 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            406 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            407 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            408 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            409 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            410 => function ($stackPos) {
                 $this->semValue = new Expr\PostInc($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            411 => function ($stackPos) {
                 $this->semValue = new Expr\PreInc($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            412 => function ($stackPos) {
                 $this->semValue = new Expr\PostDec($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            413 => function ($stackPos) {
                 $this->semValue = new Expr\PreDec($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            414 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            415 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            416 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            417 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            418 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            419 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            420 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            421 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            422 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            423 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            424 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            425 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            426 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            427 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            428 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            429 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            430 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            431 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            432 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryPlus($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            433 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryMinus($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            434 => function ($stackPos) {
                 $this->semValue = new Expr\BooleanNot($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            435 => function ($stackPos) {
                 $this->semValue = new Expr\BitwiseNot($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            436 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Identical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            437 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotIdentical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            438 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Equal($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            439 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            440 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Spaceship($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            441 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Smaller($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            442 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\SmallerOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            443 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Greater($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            444 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\GreaterOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            445 => function ($stackPos) {
                 $this->semValue = new Expr\Instanceof_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            446 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            447 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(5-1)], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            448 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(4-1)], null, $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            449 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            450 => function ($stackPos) {
                 $this->semValue = new Expr\Isset_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            451 => function ($stackPos) {
                 $this->semValue = new Expr\Empty_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            452 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            453 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE_ONCE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            454 => function ($stackPos) {
                 $this->semValue = new Expr\Eval_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            455 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            456 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE_ONCE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            457 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Int_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            458 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes;
            $attrs['kind'] = $this->getFloatCastKind($this->semStack[$stackPos-(2-1)]);
            $this->semValue = new Expr\Cast\Double($this->semStack[$stackPos-(2-2)], $attrs);
            },
            459 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\String_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            460 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Array_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            461 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Object_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            462 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Bool_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            463 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Unset_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            464 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes;
            $attrs['kind'] = strtolower($this->semStack[$stackPos-(2-1)]) === 'exit' ? Expr\Exit_::KIND_EXIT : Expr\Exit_::KIND_DIE;
            $this->semValue = new Expr\Exit_($this->semStack[$stackPos-(2-2)], $attrs);
            },
            465 => function ($stackPos) {
                 $this->semValue = new Expr\ErrorSuppress($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            466 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            467 => function ($stackPos) {
                 $this->semValue = new Expr\ShellExec($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            468 => function ($stackPos) {
                 $this->semValue = new Expr\Print_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            469 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_(null, null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            470 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(2-2)], null, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            471 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            472 => function ($stackPos) {
                 $this->semValue = new Expr\YieldFrom($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            473 => function ($stackPos) {
                 $this->semValue = new Expr\Throw_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            474 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-4)], 'returnType' => $this->semStack[$stackPos-(8-6)], 'expr' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            475 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'returnType' => $this->semStack[$stackPos-(9-7)], 'expr' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            476 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-4)], 'uses' => $this->semStack[$stackPos-(8-6)], 'returnType' => $this->semStack[$stackPos-(8-7)], 'stmts' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            477 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'uses' => $this->semStack[$stackPos-(9-7)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            478 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'returnType' => $this->semStack[$stackPos-(9-7)], 'expr' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            479 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-6)], 'returnType' => $this->semStack[$stackPos-(10-8)], 'expr' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            480 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'uses' => $this->semStack[$stackPos-(9-7)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            481 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-6)], 'uses' => $this->semStack[$stackPos-(10-8)], 'returnType' => $this->semStack[$stackPos-(10-9)], 'stmts' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            482 => function ($stackPos) {
                 $this->semValue = array(new Stmt\Class_(null, ['type' => $this->semStack[$stackPos-(8-2)], 'extends' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes), $this->semStack[$stackPos-(8-3)]);
            $this->checkClass($this->semValue[0], -1);
            },
            483 => function ($stackPos) {
                 $this->semValue = new Expr\New_($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            484 => function ($stackPos) {
                 list($class, $ctorArgs) = $this->semStack[$stackPos-(2-2)]; $this->semValue = new Expr\New_($class, $ctorArgs, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            485 => function ($stackPos) {
                 $this->semValue = array();
            },
            486 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            487 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            488 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            489 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            490 => function ($stackPos) {
                 $this->semValue = new Expr\ClosureUse($this->semStack[$stackPos-(2-2)], $this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            491 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            492 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            493 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            494 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            495 => function ($stackPos) {
                 $this->semValue = new Expr\StaticCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            496 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            497 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            498 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            499 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            500 => function ($stackPos) {
                 $this->semValue = new Name\FullyQualified(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            501 => function ($stackPos) {
                 $this->semValue = new Name\Relative(substr($this->semStack[$stackPos-(1-1)], 10), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            502 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            503 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            504 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            505 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->errorState = 2;
            },
            506 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            507 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            508 => function ($stackPos) {
                 $this->semValue = null;
            },
            509 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            510 => function ($stackPos) {
                 $this->semValue = array();
            },
            511 => function ($stackPos) {
                 $this->semValue = array(new Scalar\EncapsedStringPart(Scalar\String_::parseEscapeSequences($this->semStack[$stackPos-(1-1)], '`'), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes));
            },
            512 => function ($stackPos) {
                 foreach ($this->semStack[$stackPos-(1-1)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', true); } }; $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            513 => function ($stackPos) {
                 $this->semValue = array();
            },
            514 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            515 => function ($stackPos) {
                 $this->semValue = new Expr\ConstFetch($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            516 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Line($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            517 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\File($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            518 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Dir($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            519 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Class_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            520 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Trait_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            521 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Method($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            522 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Function_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            523 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Namespace_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            524 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            525 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(5-1)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            526 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], new Expr\Error($this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)]), $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->errorState = 2;
            },
            527 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_SHORT;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(3-2)], $attrs);
            },
            528 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_LONG;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(4-3)], $attrs);
            },
            529 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            530 => function ($stackPos) {
                 $this->semValue = Scalar\String_::fromString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            531 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Scalar\String_::KIND_DOUBLE_QUOTED;
            foreach ($this->semStack[$stackPos-(3-2)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '"', true); } }; $this->semValue = new Scalar\Encapsed($this->semStack[$stackPos-(3-2)], $attrs);
            },
            532 => function ($stackPos) {
                 $this->semValue = $this->parseLNumber($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            533 => function ($stackPos) {
                 $this->semValue = Scalar\DNumber::fromString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            534 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            535 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            536 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            537 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)], true);
            },
            538 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(2-1)], '', $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(2-2)] + $this->endAttributeStack[$stackPos-(2-2)], true);
            },
            539 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)], true);
            },
            540 => function ($stackPos) {
                 $this->semValue = null;
            },
            541 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            542 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            543 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            544 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            545 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            546 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            547 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            548 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            549 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            550 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            551 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            552 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            553 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            554 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            555 => function ($stackPos) {
                 $this->semValue = new Expr\MethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            556 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafeMethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            557 => function ($stackPos) {
                 $this->semValue = null;
            },
            558 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            559 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            560 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            561 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            562 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            563 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            564 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            565 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            566 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(new Expr\Error($this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes), $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes); $this->errorState = 2;
            },
            567 => function ($stackPos) {
                 $var = $this->semStack[$stackPos-(1-1)]->name; $this->semValue = \is_string($var) ? new Node\VarLikeIdentifier($var, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes) : $var;
            },
            568 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            569 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            570 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            571 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            572 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            573 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            574 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            575 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            576 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            577 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            578 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            579 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            580 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            581 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            582 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->errorState = 2;
            },
            583 => function ($stackPos) {
                 $this->semValue = new Expr\List_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            584 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $end = count($this->semValue)-1; if ($this->semValue[$end] === null) array_pop($this->semValue);
            },
            585 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            586 => function ($stackPos) {
                 /* do nothing -- prevent default action of $$=$this->semStack[$1]. See $551. */
            },
            587 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            588 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            589 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            590 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(2-2)], null, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            591 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            592 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            593 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-1)], true, $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            594 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            595 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(2-2)], null, false, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes, true);
            },
            596 => function ($stackPos) {
                 $this->semValue = null;
            },
            597 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            598 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            599 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            600 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]);
            },
            601 => function ($stackPos) {
                 $this->semValue = new Scalar\EncapsedStringPart($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            602 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            603 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            604 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            605 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            606 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            607 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            608 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            609 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-4)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            610 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            611 => function ($stackPos) {
                 $this->semValue = new Scalar\String_($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            612 => function ($stackPos) {
                 $this->semValue = $this->parseNumString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            613 => function ($stackPos) {
                 $this->semValue = $this->parseNumString('-' . $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            614 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
        ];
    }
}
