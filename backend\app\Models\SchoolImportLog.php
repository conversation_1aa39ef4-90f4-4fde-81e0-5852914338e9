<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SchoolImportLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'filename',
        'file_size',
        'file_path',
        'parent_id',
        'user_id',
        'status',
        'total_rows',
        'success_rows',
        'failed_rows',
        'error_details',
        'warning_details',
        'import_options',
        'started_at',
        'completed_at'
    ];

    protected $casts = [
        'file_size' => 'integer',
        'total_rows' => 'integer',
        'success_rows' => 'integer',
        'failed_rows' => 'integer',
        'error_details' => 'array',
        'warning_details' => 'array',
        'import_options' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $attributes = [
        'status' => 'pending',
        'total_rows' => 0,
        'success_rows' => 0,
        'failed_rows' => 0
    ];

    /**
     * 状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCESS = 'success';
    const STATUS_PARTIAL_SUCCESS = 'partial_success';
    const STATUS_FAILED = 'failed';

    /**
     * 状态映射
     */
    public static $statusMap = [
        self::STATUS_PENDING => '等待处理',
        self::STATUS_PROCESSING => '处理中',
        self::STATUS_SUCCESS => '成功',
        self::STATUS_PARTIAL_SUCCESS => '部分成功',
        self::STATUS_FAILED => '失败'
    ];

    /**
     * 父级组织关系
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'parent_id');
    }

    /**
     * 导入用户关系
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return self::$statusMap[$this->status] ?? '未知';
    }

    /**
     * 获取成功率
     */
    public function getSuccessRateAttribute(): float
    {
        if ($this->total_rows == 0) {
            return 0;
        }
        
        return round(($this->success_rows / $this->total_rows) * 100, 2);
    }

    /**
     * 获取失败率
     */
    public function getFailureRateAttribute(): float
    {
        if ($this->total_rows == 0) {
            return 0;
        }
        
        return round(($this->failed_rows / $this->total_rows) * 100, 2);
    }

    /**
     * 获取处理时长（秒）
     */
    public function getDurationAttribute(): ?int
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }
        
        return $this->completed_at->diffInSeconds($this->started_at);
    }

    /**
     * 获取格式化的文件大小
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * 检查是否完成
     */
    public function isCompleted(): bool
    {
        return in_array($this->status, [
            self::STATUS_SUCCESS,
            self::STATUS_PARTIAL_SUCCESS,
            self::STATUS_FAILED
        ]);
    }

    /**
     * 检查是否成功
     */
    public function isSuccessful(): bool
    {
        return $this->status === self::STATUS_SUCCESS;
    }

    /**
     * 检查是否失败
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * 检查是否部分成功
     */
    public function isPartialSuccess(): bool
    {
        return $this->status === self::STATUS_PARTIAL_SUCCESS;
    }

    /**
     * 获取错误摘要
     */
    public function getErrorSummary(): array
    {
        if (empty($this->error_details)) {
            return [];
        }

        $errorTypes = [];
        foreach ($this->error_details as $error) {
            if (isset($error['errors'])) {
                foreach ($error['errors'] as $errorMsg) {
                    $errorTypes[$errorMsg] = ($errorTypes[$errorMsg] ?? 0) + 1;
                }
            }
        }

        return $errorTypes;
    }

    /**
     * 获取警告摘要
     */
    public function getWarningSummary(): array
    {
        if (empty($this->warning_details)) {
            return [];
        }

        $warningTypes = [];
        foreach ($this->warning_details as $warning) {
            if (isset($warning['message'])) {
                $key = substr($warning['message'], 0, 50) . '...';
                $warningTypes[$key] = ($warningTypes[$key] ?? 0) + 1;
            }
        }

        return $warningTypes;
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按时间范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 作用域：最近的记录
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
