using System;
using System.Collections.Generic;
using System.Text;

namespace ClassDiagrams
{
    public class PHPExcel_Reader_Serialized : PHPExcel_Reader_IReader
    {
        #region IReader Members

        public PHPExcel reads
        {
            get
            {
                throw new Exception("The method or operation is not implemented.");
            }
            set
            {
                throw new Exception("The method or operation is not implemented.");
            }
        }

        #endregion
    }

    public class PHPExcel_Reader_CSV : PHPExcel_Reader_IReader
    {
        #region IReader Members

        public PHPExcel reads
        {
            get
            {
                throw new Exception("The method or operation is not implemented.");
            }
            set
            {
                throw new Exception("The method or operation is not implemented.");
            }
        }

        #endregion
    }
}
