<?php

/*
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array(
    'year' => ':count il',
    'y' => ':count il',
    'month' => ':count ay',
    'm' => ':count ay',
    'week' => ':count həftə',
    'w' => ':count həftə',
    'day' => ':count gün',
    'd' => ':count gün',
    'hour' => ':count saat',
    'h' => ':count saat',
    'minute' => ':count dəqiqə',
    'min' => ':count dəqiqə',
    'second' => ':count saniyə',
    's' => ':count saniyə',
    'ago' => ':time əvvəl',
    'from_now' => ':time sonra',
    'after' => ':time sonra',
    'before' => ':time əvvəl',
    'diff_now' => 'indi',
    'diff_yesterday' => 'dünən',
    'diff_tomorrow' => 'sabah',
    'diff_before_yesterday' => 'srağagün',
    'diff_after_tomorrow' => 'birisi gün',
    'period_recurrences' => ':count dəfədən bir',
    'period_interval' => 'hər :interval',
    'period_start_date' => ':date tarixindən başlayaraq',
    'period_end_date' => ':date tarixinədək',
);
