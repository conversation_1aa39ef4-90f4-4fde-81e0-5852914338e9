<template>
  <div class="school-management">
    <div class="page-header">
      <h2>学校信息管理</h2>
      <div class="header-actions">
        <el-button-group>
          <el-button 
            :type="activeTab === 'import' ? 'primary' : ''"
            @click="activeTab = 'import'"
          >
            批量导入
          </el-button>
          <el-button 
            :type="activeTab === 'dashboard' ? 'primary' : ''"
            @click="activeTab = 'dashboard'"
          >
            统计仪表板
          </el-button>
          <el-button 
            :type="activeTab === 'list' ? 'primary' : ''"
            @click="activeTab = 'list'"
          >
            学校列表
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 标签页内容 -->
    <div class="tab-content">
      <!-- 批量导入 -->
      <div v-show="activeTab === 'import'" class="tab-pane">
        <SchoolImport @import-success="handleImportSuccess" />
      </div>

      <!-- 统计仪表板 -->
      <div v-show="activeTab === 'dashboard'" class="tab-pane">
        <SchoolImportDashboard @view-history="viewImportHistory" />
      </div>

      <!-- 学校列表 -->
      <div v-show="activeTab === 'list'" class="tab-pane">
        <SchoolList />
      </div>
    </div>

    <!-- 导入历史对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      title="导入历史"
      width="90%"
      :before-close="handleHistoryDialogClose"
    >
      <div class="import-history-content">
        <div class="history-filters">
          <el-form :inline="true" :model="historyFilters" class="filter-form">
            <el-form-item label="文件名">
              <el-input 
                v-model="historyFilters.filename" 
                placeholder="请输入文件名"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="状态">
              <el-select 
                v-model="historyFilters.status" 
                placeholder="请选择状态"
                clearable
                style="width: 150px"
              >
                <el-option label="成功" value="success" />
                <el-option label="失败" value="failed" />
                <el-option label="部分成功" value="partial_success" />
                <el-option label="处理中" value="processing" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="historyFilters.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 240px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchHistory" :loading="historyLoading">
                搜索
              </el-button>
              <el-button @click="resetHistoryFilters">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table 
          :data="importHistory" 
          style="width: 100%"
          v-loading="historyLoading"
        >
          <el-table-column prop="filename" label="文件名" width="200" />
          <el-table-column prop="parent.name" label="父级组织" width="150" />
          <el-table-column prop="total_rows" label="总行数" width="100" />
          <el-table-column prop="success_rows" label="成功" width="80" />
          <el-table-column prop="failed_rows" label="失败" width="80" />
          <el-table-column prop="success_rate" label="成功率" width="120">
            <template #default="{ row }">
              <el-progress 
                :percentage="row.success_rate" 
                :stroke-width="6"
                :show-text="false"
                :color="getProgressColor(row.success_rate)"
              />
              <span class="success-rate-text">{{ row.success_rate }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="status_text" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ row.status_text }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="user.real_name" label="导入用户" width="120" />
          <el-table-column prop="created_at" label="导入时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button 
                type="primary" 
                size="small" 
                @click="viewImportDetail(row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="historyPagination.current_page"
            v-model:page-size="historyPagination.per_page"
            :page-sizes="[10, 20, 50, 100]"
            :total="historyPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadImportHistory"
            @current-change="loadImportHistory"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 导入详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="导入详情"
      width="80%"
      :before-close="handleDetailDialogClose"
    >
      <div v-if="selectedImportLog" class="import-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="文件名">
            {{ selectedImportLog.filename }}
          </el-descriptions-item>
          <el-descriptions-item label="文件大小">
            {{ selectedImportLog.formatted_file_size }}
          </el-descriptions-item>
          <el-descriptions-item label="父级组织">
            {{ selectedImportLog.parent?.name || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="导入用户">
            {{ selectedImportLog.user?.real_name || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedImportLog.status)">
              {{ selectedImportLog.status_text }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="成功率">
            {{ selectedImportLog.success_rate }}%
          </el-descriptions-item>
          <el-descriptions-item label="总行数">
            {{ selectedImportLog.total_rows }}
          </el-descriptions-item>
          <el-descriptions-item label="成功行数">
            {{ selectedImportLog.success_rows }}
          </el-descriptions-item>
          <el-descriptions-item label="失败行数">
            {{ selectedImportLog.failed_rows }}
          </el-descriptions-item>
          <el-descriptions-item label="处理时长">
            {{ selectedImportLog.duration ? selectedImportLog.duration + '秒' : '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ formatDateTime(selectedImportLog.started_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="完成时间">
            {{ formatDateTime(selectedImportLog.completed_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 错误详情 -->
        <div v-if="selectedImportLog.error_details && selectedImportLog.error_details.length > 0" class="error-section">
          <h4>错误详情</h4>
          <el-table :data="selectedImportLog.error_details" style="width: 100%" max-height="300">
            <el-table-column prop="row" label="行号" width="80" />
            <el-table-column prop="errors" label="错误信息">
              <template #default="{ row }">
                <div class="error-list">
                  <div v-for="(error, index) in row.errors" :key="index" class="error-item">
                    {{ error }}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import SchoolImport from '../../components/organization/SchoolImport.vue'
import SchoolImportDashboard from '../../components/organization/SchoolImportDashboard.vue'
import SchoolList from '../../components/organization/SchoolList.vue'
import { schoolImportApi } from '../../api/schoolImport'

// 响应式数据
const activeTab = ref('import')

// 导入历史相关
const historyDialogVisible = ref(false)
const historyLoading = ref(false)
const importHistory = ref([])
const historyPagination = reactive({
  current_page: 1,
  per_page: 20,
  total: 0
})

const historyFilters = reactive({
  filename: '',
  status: '',
  dateRange: null
})

// 导入详情相关
const detailDialogVisible = ref(false)
const selectedImportLog = ref(null)

// 方法
const handleImportSuccess = () => {
  // 导入成功后切换到仪表板查看统计
  activeTab.value = 'dashboard'
  ElMessage.success('导入成功，已切换到统计仪表板')
}

const viewImportHistory = () => {
  historyDialogVisible.value = true
  loadImportHistory()
}

const loadImportHistory = async () => {
  historyLoading.value = true
  try {
    const params = {
      page: historyPagination.current_page,
      per_page: historyPagination.per_page,
      ...historyFilters
    }
    
    // 处理日期范围
    if (historyFilters.dateRange && historyFilters.dateRange.length === 2) {
      params.start_date = historyFilters.dateRange[0]
      params.end_date = historyFilters.dateRange[1]
    }
    
    const response = await schoolImportApi.getHistory(params)
    const data = response.data.data
    
    importHistory.value = data.data
    historyPagination.total = data.total
    historyPagination.current_page = data.current_page
    historyPagination.per_page = data.per_page
  } catch (error) {
    console.error('加载导入历史失败:', error)
    ElMessage.error('加载导入历史失败')
  } finally {
    historyLoading.value = false
  }
}

const searchHistory = () => {
  historyPagination.current_page = 1
  loadImportHistory()
}

const resetHistoryFilters = () => {
  historyFilters.filename = ''
  historyFilters.status = ''
  historyFilters.dateRange = null
  searchHistory()
}

const viewImportDetail = async (importLog) => {
  try {
    const response = await schoolImportApi.getDetail(importLog.id)
    selectedImportLog.value = response.data.data
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取导入详情失败:', error)
    ElMessage.error('获取导入详情失败')
  }
}

const handleHistoryDialogClose = () => {
  historyDialogVisible.value = false
}

const handleDetailDialogClose = () => {
  detailDialogVisible.value = false
  selectedImportLog.value = null
}

const getProgressColor = (percentage) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'info',
    'processing': 'warning',
    'success': 'success',
    'partial_success': 'warning',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '无'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 页面加载时默认显示导入页面
})
</script>

<style scoped>
.school-management {
  padding: 20px;
  min-height: calc(100vh - 120px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.tab-content {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.tab-pane {
  min-height: 600px;
}

/* 导入历史对话框样式 */
.import-history-content {
  max-height: 70vh;
  overflow-y: auto;
}

.history-filters {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.filter-form {
  margin: 0;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

.success-rate-text {
  margin-left: 8px;
  font-size: 12px;
  color: #606266;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 导入详情样式 */
.import-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.error-section {
  margin-top: 20px;
}

.error-section h4 {
  margin: 0 0 12px 0;
  color: #f56c6c;
  font-size: 14px;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.error-item {
  padding: 4px 8px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .school-management {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .history-filters {
    padding: 12px;
  }

  .filter-form {
    display: block;
  }

  .filter-form .el-form-item {
    margin-bottom: 12px;
    display: block;
  }

  .filter-form .el-form-item:last-child {
    margin-bottom: 0;
  }
}

/* 表格样式调整 */
:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table .cell) {
  padding: 8px 12px;
}

/* 进度条样式 */
:deep(.el-progress-bar__outer) {
  border-radius: 4px;
  height: 6px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
}

/* 按钮组样式 */
:deep(.el-button-group .el-button) {
  border-radius: 0;
}

:deep(.el-button-group .el-button:first-child) {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

:deep(.el-button-group .el-button:last-child) {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

/* 描述列表样式 */
:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}
</style>
