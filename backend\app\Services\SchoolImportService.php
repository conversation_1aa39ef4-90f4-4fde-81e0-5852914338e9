<?php

namespace App\Services;

use App\Models\Organization;
use App\Models\SchoolImportLog;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Exception;

class SchoolImportService
{
    /**
     * 支持的文件类型
     */
    const SUPPORTED_EXTENSIONS = ['xlsx', 'xls', 'csv'];

    /**
     * 学校类型映射
     */
    const SCHOOL_TYPES = [
        '小学' => 'primary',
        '初中' => 'junior_high',
        '高中' => 'senior_high',
        '九年一贯制' => 'nine_year',
        '完全中学' => 'complete_middle',
        '职业学校' => 'vocational',
        '特殊教育学校' => 'special_education'
    ];

    /**
     * 教育层次映射
     */
    const EDUCATION_LEVELS = [
        '学前教育' => 'preschool',
        '小学教育' => 'primary',
        '初中教育' => 'junior_high',
        '高中教育' => 'senior_high',
        '中等职业教育' => 'vocational',
        '特殊教育' => 'special'
    ];

    /**
     * 导入学校数据
     */
    public function import($file, $parentId = null, $overwrite = false, $validateOnly = false, User $user = null)
    {
        $importLog = null;
        
        try {
            // 创建导入日志
            if (!$validateOnly) {
                $importLog = SchoolImportLog::create([
                    'filename' => $file->getClientOriginalName(),
                    'file_size' => $file->getSize(),
                    'file_path' => $file->store('imports/schools', 'public'),
                    'parent_id' => $parentId,
                    'user_id' => $user ? $user->id : null,
                    'status' => 'processing',
                    'import_options' => [
                        'overwrite' => $overwrite,
                        'validate_only' => $validateOnly
                    ]
                ]);
            }

            // 读取文件数据
            $data = $this->readFile($file);
            
            if (empty($data)) {
                throw new Exception('文件为空或格式不正确');
            }

            // 验证表头
            $header = array_shift($data);
            $this->validateHeader($header);

            // 处理数据
            $results = [
                'total' => count($data),
                'success' => 0,
                'failed' => 0,
                'errors' => [],
                'warnings' => [],
                'import_log_id' => $importLog ? $importLog->id : null
            ];

            if (!$validateOnly) {
                DB::beginTransaction();
            }

            foreach ($data as $index => $row) {
                $rowNumber = $index + 2; // Excel行号（从2开始，因为第1行是表头）
                
                try {
                    $schoolData = $this->parseRowData($row, $header, $parentId);
                    
                    // 验证数据
                    $validator = $this->validateSchoolData($schoolData);
                    
                    if ($validator->fails()) {
                        $results['failed']++;
                        $results['errors'][] = [
                            'row' => $rowNumber,
                            'data' => $schoolData,
                            'errors' => $validator->errors()->all()
                        ];
                        continue;
                    }

                    // 检查权限
                    if ($user && !$this->checkUserPermission($user, $schoolData['parent_id'] ?? $parentId)) {
                        $results['failed']++;
                        $results['errors'][] = [
                            'row' => $rowNumber,
                            'data' => $schoolData,
                            'errors' => ['无权在指定父级组织下创建学校']
                        ];
                        continue;
                    }

                    // 检查重复
                    $existingSchool = $this->findExistingSchool($schoolData);
                    if ($existingSchool && !$overwrite) {
                        $results['warnings'][] = [
                            'row' => $rowNumber,
                            'data' => $schoolData,
                            'message' => "学校已存在：{$existingSchool->name} (代码: {$existingSchool->code})"
                        ];
                        continue;
                    }

                    if (!$validateOnly) {
                        // 创建或更新学校
                        if ($existingSchool && $overwrite) {
                            $existingSchool->update($schoolData);
                            $results['success']++;
                        } else {
                            Organization::create($schoolData);
                            $results['success']++;
                        }
                    } else {
                        $results['success']++;
                    }

                } catch (Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = [
                        'row' => $rowNumber,
                        'data' => $schoolData ?? [],
                        'errors' => [$e->getMessage()]
                    ];
                    
                    Log::error('学校导入行处理失败', [
                        'row' => $rowNumber,
                        'error' => $e->getMessage(),
                        'data' => $schoolData ?? []
                    ]);
                }
            }

            if (!$validateOnly) {
                if ($results['failed'] > 0 && $results['success'] == 0) {
                    DB::rollBack();
                    throw new Exception('所有数据导入失败');
                } else {
                    DB::commit();
                }

                // 更新导入日志
                if ($importLog) {
                    $importLog->update([
                        'status' => $results['failed'] > 0 ? 'partial_success' : 'success',
                        'total_rows' => $results['total'],
                        'success_rows' => $results['success'],
                        'failed_rows' => $results['failed'],
                        'error_details' => $results['errors'],
                        'warning_details' => $results['warnings'],
                        'completed_at' => now()
                    ]);
                }
            }

            return $results;

        } catch (Exception $e) {
            if (!$validateOnly) {
                DB::rollBack();
            }

            if ($importLog) {
                $importLog->update([
                    'status' => 'failed',
                    'error_details' => [['error' => $e->getMessage()]],
                    'completed_at' => now()
                ]);
            }

            Log::error('学校导入失败', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName(),
                'user_id' => $user ? $user->id : null
            ]);

            throw $e;
        }
    }

    /**
     * 读取文件数据
     */
    private function readFile($file)
    {
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($extension, self::SUPPORTED_EXTENSIONS)) {
            throw new Exception('不支持的文件格式，请使用 Excel (.xlsx, .xls) 或 CSV (.csv) 文件');
        }

        try {
            if ($extension === 'csv') {
                return $this->readCsvFile($file);
            } else {
                return $this->readExcelFile($file);
            }
        } catch (Exception $e) {
            throw new Exception('文件读取失败：' . $e->getMessage());
        }
    }

    /**
     * 读取CSV文件
     */
    private function readCsvFile($file)
    {
        $data = [];
        $handle = fopen($file->getRealPath(), 'r');
        
        if ($handle !== false) {
            while (($row = fgetcsv($handle, 0, ',')) !== false) {
                $data[] = array_map('trim', $row);
            }
            fclose($handle);
        }
        
        return $data;
    }

    /**
     * 读取Excel文件
     */
    private function readExcelFile($file)
    {
        $spreadsheet = IOFactory::load($file->getRealPath());
        $worksheet = $spreadsheet->getActiveSheet();
        $data = [];
        
        foreach ($worksheet->getRowIterator() as $row) {
            $rowData = [];
            foreach ($row->getCellIterator() as $cell) {
                $rowData[] = trim($cell->getCalculatedValue());
            }
            $data[] = $rowData;
        }
        
        return $data;
    }

    /**
     * 验证表头
     */
    private function validateHeader($header)
    {
        $requiredHeaders = [
            '学校名称', '学校代码', '学校类型', '教育层次', '校长姓名',
            '校长电话', '联系人', '联系电话', '学校地址'
        ];

        $missingHeaders = [];
        foreach ($requiredHeaders as $required) {
            if (!in_array($required, $header)) {
                $missingHeaders[] = $required;
            }
        }

        if (!empty($missingHeaders)) {
            throw new Exception('缺少必需的表头字段：' . implode(', ', $missingHeaders));
        }
    }

    /**
     * 解析行数据
     */
    private function parseRowData($row, $header, $parentId)
    {
        $data = array_combine($header, $row);

        // 获取父级组织信息
        $parent = null;
        if ($parentId) {
            $parent = Organization::find($parentId);
            if (!$parent) {
                throw new Exception('指定的父级组织不存在');
            }
        }

        // 解析学校类型
        $schoolType = $this->parseSchoolType($data['学校类型'] ?? '');
        $educationLevel = $this->parseEducationLevel($data['教育层次'] ?? '');

        return [
            'name' => $data['学校名称'] ?? '',
            'code' => $data['学校代码'] ?? '',
            'school_code' => $data['学校代码'] ?? '',
            'type' => 'school',
            'school_type' => $schoolType,
            'education_level' => $educationLevel,
            'parent_id' => $parentId,
            'level' => $parent ? $parent->level + 1 : 5, // 学校通常是最底层
            'description' => $data['学校描述'] ?? '',
            'status' => 'active',

            // 学校特有字段
            'principal_name' => $data['校长姓名'] ?? '',
            'principal_phone' => $data['校长电话'] ?? '',
            'principal_email' => $data['校长邮箱'] ?? '',
            'contact_person' => $data['联系人'] ?? '',
            'contact_phone' => $data['联系电话'] ?? '',
            'address' => $data['学校地址'] ?? '',
            'student_count' => $this->parseInteger($data['学生人数'] ?? ''),
            'campus_area' => $this->parseDecimal($data['校园面积'] ?? ''),
            'founded_year' => $this->parseInteger($data['建校年份'] ?? ''),
            'longitude' => $this->parseDecimal($data['经度'] ?? ''),
            'latitude' => $this->parseDecimal($data['纬度'] ?? ''),

            // 额外信息
            'extra_data' => [
                'class_count' => $this->parseInteger($data['班级数'] ?? ''),
                'teacher_count' => $this->parseInteger($data['教师人数'] ?? ''),
                'facilities' => $data['设施设备'] ?? '',
                'special_programs' => $data['特色项目'] ?? ''
            ]
        ];
    }

    /**
     * 解析学校类型
     */
    private function parseSchoolType($type)
    {
        return self::SCHOOL_TYPES[$type] ?? 'primary';
    }

    /**
     * 解析教育层次
     */
    private function parseEducationLevel($level)
    {
        return self::EDUCATION_LEVELS[$level] ?? 'primary';
    }

    /**
     * 解析整数
     */
    private function parseInteger($value)
    {
        if (empty($value) || !is_numeric($value)) {
            return null;
        }
        return (int) $value;
    }

    /**
     * 解析小数
     */
    private function parseDecimal($value)
    {
        if (empty($value) || !is_numeric($value)) {
            return null;
        }
        return (float) $value;
    }

    /**
     * 验证学校数据
     */
    private function validateSchoolData($data)
    {
        $rules = [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:organizations,code',
            'school_code' => 'required|string|max:50',
            'school_type' => 'required|string|in:' . implode(',', array_values(self::SCHOOL_TYPES)),
            'education_level' => 'required|string|in:' . implode(',', array_values(self::EDUCATION_LEVELS)),
            'principal_name' => 'required|string|max:100',
            'principal_phone' => 'required|string|max:20',
            'principal_email' => 'nullable|email|max:255',
            'contact_person' => 'required|string|max:100',
            'contact_phone' => 'required|string|max:20',
            'address' => 'required|string|max:500',
            'student_count' => 'nullable|integer|min:0',
            'campus_area' => 'nullable|numeric|min:0',
            'founded_year' => 'nullable|integer|min:1900|max:' . date('Y'),
            'longitude' => 'nullable|numeric|between:-180,180',
            'latitude' => 'nullable|numeric|between:-90,90'
        ];

        $messages = [
            'name.required' => '学校名称不能为空',
            'name.max' => '学校名称不能超过255个字符',
            'code.required' => '学校代码不能为空',
            'code.unique' => '学校代码已存在',
            'code.max' => '学校代码不能超过50个字符',
            'school_code.required' => '学校代码不能为空',
            'school_type.required' => '学校类型不能为空',
            'school_type.in' => '学校类型不正确',
            'education_level.required' => '教育层次不能为空',
            'education_level.in' => '教育层次不正确',
            'principal_name.required' => '校长姓名不能为空',
            'principal_phone.required' => '校长电话不能为空',
            'principal_email.email' => '校长邮箱格式不正确',
            'contact_person.required' => '联系人不能为空',
            'contact_phone.required' => '联系电话不能为空',
            'address.required' => '学校地址不能为空',
            'student_count.integer' => '学生人数必须是整数',
            'student_count.min' => '学生人数不能小于0',
            'campus_area.numeric' => '校园面积必须是数字',
            'campus_area.min' => '校园面积不能小于0',
            'founded_year.integer' => '建校年份必须是整数',
            'founded_year.min' => '建校年份不能早于1900年',
            'founded_year.max' => '建校年份不能晚于当前年份',
            'longitude.between' => '经度必须在-180到180之间',
            'latitude.between' => '纬度必须在-90到90之间'
        ];

        return Validator::make($data, $rules, $messages);
    }

    /**
     * 检查用户权限
     */
    private function checkUserPermission(User $user, $organizationId)
    {
        if (!$organizationId) {
            return false;
        }

        // 检查用户是否有权限在指定组织下创建学校
        $accessScope = $user->getDataAccessScope();

        if ($accessScope['type'] === 'all') {
            return true;
        }

        if ($accessScope['type'] === 'specific') {
            return in_array($organizationId, $accessScope['organizations']);
        }

        return false;
    }

    /**
     * 查找已存在的学校
     */
    private function findExistingSchool($data)
    {
        return Organization::where('type', 'school')
            ->where(function ($query) use ($data) {
                $query->where('code', $data['code'])
                      ->orWhere('school_code', $data['school_code']);
            })
            ->first();
    }

    /**
     * 生成学校导入模板
     */
    public function generateTemplate()
    {
        $headers = [
            '学校名称',
            '学校代码',
            '学校类型',
            '教育层次',
            '校长姓名',
            '校长电话',
            '校长邮箱',
            '联系人',
            '联系电话',
            '学校地址',
            '学生人数',
            '班级数',
            '教师人数',
            '校园面积',
            '建校年份',
            '经度',
            '纬度',
            '设施设备',
            '特色项目',
            '学校描述'
        ];

        $sampleData = [
            [
                '东城小学',
                'DC001',
                '小学',
                '小学教育',
                '张校长',
                '0311-88123456',
                '<EMAIL>',
                '李老师',
                '0311-88123457',
                '河北省石家庄市藁城区廉州镇东城村',
                '500',
                '18',
                '35',
                '15000',
                '1985',
                '114.8493',
                '38.0428',
                '教学楼、实验室、图书馆、体育馆',
                '书法特色、科技创新',
                '藁城区廉州学区东城小学，办学历史悠久'
            ],
            [
                '西城小学',
                'XC001',
                '小学',
                '小学教育',
                '王校长',
                '0311-88234567',
                '<EMAIL>',
                '赵老师',
                '0311-88234568',
                '河北省石家庄市藁城区廉州镇西城村',
                '420',
                '15',
                '28',
                '12000',
                '1990',
                '114.8393',
                '38.0328',
                '教学楼、多媒体教室、操场',
                '音乐特色、体育强项',
                '藁城区廉州学区西城小学，注重全面发展'
            ]
        ];

        return [
            'headers' => $headers,
            'sample_data' => $sampleData,
            'school_types' => array_keys(self::SCHOOL_TYPES),
            'education_levels' => array_keys(self::EDUCATION_LEVELS)
        ];
    }

    /**
     * 生成Excel模板文件
     */
    public function generateExcelTemplate()
    {
        $template = $this->generateTemplate();

        // 这里可以使用PhpSpreadsheet生成更复杂的Excel模板
        // 包括数据验证、下拉列表等
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $worksheet = $spreadsheet->getActiveSheet();

        // 设置表头
        $worksheet->fromArray($template['headers'], null, 'A1');

        // 设置样本数据
        $worksheet->fromArray($template['sample_data'], null, 'A2');

        // 设置列宽
        foreach (range('A', 'T') as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }

        // 设置表头样式
        $headerStyle = [
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'E2EFDA']
            ]
        ];
        $worksheet->getStyle('A1:T1')->applyFromArray($headerStyle);

        return $spreadsheet;
    }

    /**
     * 生成CSV模板内容
     */
    public function generateCsvTemplate()
    {
        $template = $this->generateTemplate();

        // 添加BOM以支持中文
        $csv = "\xEF\xBB\xBF";
        $csv .= implode(',', $template['headers']) . "\n";

        foreach ($template['sample_data'] as $row) {
            $csv .= implode(',', array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row)) . "\n";
        }

        return $csv;
    }

    /**
     * 获取导入统计信息
     */
    public function getImportStats($userId = null, $days = 30)
    {
        $query = SchoolImportLog::query();

        if ($userId) {
            $query->where('user_id', $userId);
        }

        $query->where('created_at', '>=', now()->subDays($days));

        $logs = $query->get();

        return [
            'total_imports' => $logs->count(),
            'successful_imports' => $logs->where('status', 'success')->count(),
            'failed_imports' => $logs->where('status', 'failed')->count(),
            'partial_success_imports' => $logs->where('status', 'partial_success')->count(),
            'total_schools_imported' => $logs->sum('success_rows'),
            'total_errors' => $logs->sum('failed_rows')
        ];
    }
}
