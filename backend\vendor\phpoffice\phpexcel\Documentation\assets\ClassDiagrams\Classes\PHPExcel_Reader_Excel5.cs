using System;
using System.Collections.Generic;
using System.Text;

namespace ClassDiagrams
{
    public class PHPExcel_Reader_Excel5 : PHPExcel_Reader_IReader
    {
        #region PHPExcel_Writer_IReader Members

        public PHPExcel reads
        {
            get
            {
                throw new Exception("The method or operation is not implemented.");
            }
            set
            {
                throw new Exception("The method or operation is not implemented.");
            }
        }

        #endregion
    }

    public class PHPExcel_Reader_Excel2003XML : PHPExcel_Reader_IReader
    {
        #region PHPExcel_Writer_IReader Members

        public PHPExcel reads
        {
            get
            {
                throw new Exception("The method or operation is not implemented.");
            }
            set
            {
                throw new Exception("The method or operation is not implemented.");
            }
        }

        #endregion
    }

    public class PHPExcel_Reader_SYLK : PHPExcel_Reader_IReader
    {
        #region PHPExcel_Writer_IReader Members

        public PHPExcel reads
        {
            get
            {
                throw new Exception("The method or operation is not implemented.");
            }
            set
            {
                throw new Exception("The method or operation is not implemented.");
            }
        }

        #endregion
    }
}
