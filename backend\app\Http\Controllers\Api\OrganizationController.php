<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\OrganizationRequest;
use App\Http\Requests\SchoolImportRequest;
use App\Models\Organization;
use App\Models\User;
use App\Models\SchoolImportLog;
use App\Services\SchoolImportService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class OrganizationController extends Controller
{
    /**
     * 获取组织机构列表
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        $query = Organization::query();

        // 根据用户权限过滤数据范围
        $accessScope = $user->getDataAccessScope();
        if ($accessScope['type'] === 'specific') {
            $query->whereIn('id', $accessScope['organizations']);
        } elseif ($accessScope['type'] === 'none') {
            // 如果用户没有任何权限，返回空结果
            $query->whereRaw('1 = 0');
        }
        // 如果是 'all' 类型，不添加任何过滤条件

        // 搜索条件
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // 层级过滤
        if ($request->filled('level')) {
            $query->level($request->input('level'));
        }

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->boolean('status'));
        }

        // 父级组织过滤
        if ($request->filled('parent_id')) {
            $parentId = $request->input('parent_id');
            if ($parentId === 'null') {
                $query->whereNull('parent_id');
            } else {
                $query->childrenOf($parentId);
            }
        }

        // 排序
        $sortBy = $request->input('sort_by', 'sort_order');
        $sortOrder = $request->input('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // 分页
        $perPage = $request->input('per_page', 15);
        $organizations = $query->with(['parent', 'children'])->paginate($perPage);

        return response()->json([
            'message' => '获取组织机构列表成功',
            'data' => $organizations,
            'code' => 200
        ]);
    }

    /**
     * 获取组织机构树形结构
     */
    public function tree(Request $request): JsonResponse
    {
        $user = Auth::user();
        $parentId = $request->input('parent_id');
        
        // 根据用户权限获取可访问的组织范围
        $accessScope = $user->getDataAccessScope();
        
        if ($accessScope['type'] === 'specific') {
            // 如果用户只能访问特定组织，则只显示这些组织
            $organizations = Organization::whereIn('id', $accessScope['organizations'])
                ->with(['children' => function ($query) use ($accessScope) {
                    $query->whereIn('id', $accessScope['organizations']);
                }])
                ->get();
        } else {
            // 如果用户有全局权限，则显示所有组织
            $organizations = Organization::getTree($parentId);
        }

        return response()->json([
            'message' => '获取组织机构树成功',
            'data' => $organizations,
            'code' => 200
        ]);
    }

    /**
     * 获取单个组织机构详情
     */
    public function show(Organization $organization): JsonResponse
    {
        $user = Auth::user();
        
        // 检查用户是否有权限访问该组织
        if (!$user->canAccessOrganization($organization->id)) {
            return response()->json([
                'message' => '无权访问该组织机构',
                'code' => 403
            ], 403);
        }

        $organization->load(['parent', 'children', 'users']);

        return response()->json([
            'message' => '获取组织机构详情成功',
            'data' => $organization,
            'code' => 200
        ]);
    }

    /**
     * 创建组织机构
     */
    public function store(OrganizationRequest $request): JsonResponse
    {
        $user = Auth::user();
        $data = $request->validated();

        // 检查用户是否有权限在指定父级组织下创建子组织
        if (isset($data['parent_id'])) {
            if (!$user->canAccessOrganization($data['parent_id'])) {
                return response()->json([
                    'message' => '无权在指定父级组织下创建子组织',
                    'code' => 403
                ], 403);
            }
        }

        try {
            DB::beginTransaction();

            $organization = Organization::create($data);
            
            // 更新路径和层级
            $organization->updatePath();
            $organization->updateChildrenLevel();

            DB::commit();

            $organization->load(['parent', 'children']);

            return response()->json([
                'message' => '创建组织机构成功',
                'data' => $organization,
                'code' => 201
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'message' => '创建组织机构失败: ' . $e->getMessage(),
                'code' => 500
            ], 500);
        }
    }

    /**
     * 更新组织机构
     */
    public function update(OrganizationRequest $request, Organization $organization): JsonResponse
    {
        $user = Auth::user();
        $data = $request->validated();

        // 检查用户是否有权限修改该组织
        if (!$user->canAccessOrganization($organization->id)) {
            return response()->json([
                'message' => '无权修改该组织机构',
                'code' => 403
            ], 403);
        }

        // 如果要修改父级组织，检查新父级组织的访问权限
        if (isset($data['parent_id']) && $data['parent_id'] != $organization->parent_id) {
            if (!$user->canAccessOrganization($data['parent_id'])) {
                return response()->json([
                    'message' => '无权将组织移动到指定父级组织下',
                    'code' => 403
                ], 403);
            }

            // 检查是否会造成循环引用
            if ($organization->isDescendantOf(Organization::find($data['parent_id']))) {
                return response()->json([
                    'message' => '不能将组织移动到其子组织下',
                    'code' => 422
                ], 422);
            }
        }

        try {
            DB::beginTransaction();

            $organization->update($data);
            
            // 更新路径和层级
            $organization->updatePath();
            $organization->updateChildrenLevel();

            DB::commit();

            $organization->load(['parent', 'children']);

            return response()->json([
                'message' => '更新组织机构成功',
                'data' => $organization,
                'code' => 200
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'message' => '更新组织机构失败: ' . $e->getMessage(),
                'code' => 500
            ], 500);
        }
    }

    /**
     * 删除组织机构
     */
    public function destroy(Organization $organization): JsonResponse
    {
        $user = Auth::user();

        // 检查用户是否有权限删除该组织
        if (!$user->canAccessOrganization($organization->id)) {
            return response()->json([
                'message' => '无权删除该组织机构',
                'code' => 403
            ], 403);
        }

        // 检查是否有子组织
        if ($organization->children()->count() > 0) {
            return response()->json([
                'message' => '该组织下还有子组织，无法删除',
                'code' => 422
            ], 422);
        }

        // 检查是否有关联的用户
        if ($organization->users()->count() > 0) {
            return response()->json([
                'message' => '该组织下还有用户，无法删除',
                'code' => 422
            ], 422);
        }

        try {
            $organization->delete();

            return response()->json([
                'message' => '删除组织机构成功',
                'code' => 200
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '删除组织机构失败: ' . $e->getMessage(),
                'code' => 500
            ], 500);
        }
    }

    /**
     * 移动组织机构
     */
    public function move(Request $request, Organization $organization): JsonResponse
    {
        $user = Auth::user();
        
        $request->validate([
            'parent_id' => 'nullable|exists:organizations,id',
            'position' => 'nullable|integer|min:0'
        ]);

        $parentId = $request->input('parent_id');
        $position = $request->input('position', 0);

        // 检查用户权限
        if (!$user->canAccessOrganization($organization->id)) {
            return response()->json([
                'message' => '无权操作该组织机构',
                'code' => 403
            ], 403);
        }

        if ($parentId && !$user->canAccessOrganization($parentId)) {
            return response()->json([
                'message' => '无权将组织移动到指定父级组织下',
                'code' => 403
            ], 403);
        }

        // 检查是否会造成循环引用
        if ($parentId && $organization->isDescendantOf(Organization::find($parentId))) {
            return response()->json([
                'message' => '不能将组织移动到其子组织下',
                'code' => 422
            ], 422);
        }

        try {
            DB::beginTransaction();

            $organization->update([
                'parent_id' => $parentId,
                'sort_order' => $position
            ]);

            $organization->updatePath();
            $organization->updateChildrenLevel();

            DB::commit();

            return response()->json([
                'message' => '移动组织机构成功',
                'data' => $organization->fresh(['parent', 'children']),
                'code' => 200
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'message' => '移动组织机构失败: ' . $e->getMessage(),
                'code' => 500
            ], 500);
        }
    }

    /**
     * 获取组织下的用户
     */
    public function users(Request $request, Organization $organization): JsonResponse
    {
        $user = Auth::user();

        if (!$user->canAccessOrganization($organization->id)) {
            return response()->json([
                'message' => '无权访问该组织机构',
                'code' => 403
            ], 403);
        }

        $query = $organization->users();

        // 搜索条件
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('username', 'like', "%{$search}%")
                  ->orWhere('real_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->boolean('status'));
        }

        $perPage = $request->input('per_page', 15);
        $users = $query->with(['roles', 'primaryOrganization'])->paginate($perPage);

        return response()->json([
            'message' => '获取组织用户列表成功',
            'data' => $users,
            'code' => 200
        ]);
    }

    /**
     * 获取子组织
     */
    public function children(Organization $organization): JsonResponse
    {
        $user = Auth::user();

        if (!$user->canAccessOrganization($organization->id)) {
            return response()->json([
                'message' => '无权访问该组织机构',
                'code' => 403
            ], 403);
        }

        $children = $organization->children()->with(['parent', 'children'])->get();

        return response()->json([
            'message' => '获取子组织成功',
            'data' => $children,
            'code' => 200
        ]);
    }

    /**
     * 获取祖先组织
     */
    public function ancestors(Organization $organization): JsonResponse
    {
        $user = Auth::user();

        if (!$user->canAccessOrganization($organization->id)) {
            return response()->json([
                'message' => '无权访问该组织机构',
                'code' => 403
            ], 403);
        }

        $ancestors = $organization->getAncestors();

        return response()->json([
            'message' => '获取祖先组织成功',
            'data' => $ancestors,
            'code' => 200
        ]);
    }

    /**
     * 获取后代组织
     */
    public function descendants(Organization $organization): JsonResponse
    {
        $user = Auth::user();

        if (!$user->canAccessOrganization($organization->id)) {
            return response()->json([
                'message' => '无权访问该组织机构',
                'code' => 403
            ], 403);
        }

        $descendants = $organization->getDescendants();

        return response()->json([
            'message' => '获取后代组织成功',
            'data' => $descendants,
            'code' => 200
        ]);
    }

    /**
     * 学校信息批量导入
     */
    public function importSchools(SchoolImportRequest $request, SchoolImportService $importService): JsonResponse
    {
        $user = Auth::user();
        $data = $request->getValidatedData();

        try {
            $results = $importService->import(
                $request->file('file'),
                $data['parent_id'],
                $data['overwrite'],
                $data['validate_only'],
                $user
            );

            $message = $data['validate_only'] ? '学校数据验证完成' : '学校导入完成';

            return response()->json([
                'message' => $message,
                'data' => $results,
                'code' => 200
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '学校导入失败: ' . $e->getMessage(),
                'code' => 500
            ], 500);
        }
    }

    /**
     * 下载学校导入模板
     */
    public function downloadSchoolTemplate(Request $request, SchoolImportService $importService)
    {
        $format = $request->input('format', 'excel'); // excel 或 csv

        try {
            if ($format === 'csv') {
                $csvContent = $importService->generateCsvTemplate();
                $filename = '学校信息导入模板_' . date('Y-m-d') . '.csv';

                return response($csvContent)
                    ->header('Content-Type', 'text/csv; charset=UTF-8')
                    ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                    ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                    ->header('Pragma', 'no-cache')
                    ->header('Expires', '0');
            } else {
                $spreadsheet = $importService->generateExcelTemplate();
                $filename = '学校信息导入模板_' . date('Y-m-d') . '.xlsx';

                $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

                return response()->streamDownload(function() use ($writer) {
                    $writer->save('php://output');
                }, $filename, [
                    'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'Cache-Control' => 'no-cache, no-store, must-revalidate',
                    'Pragma' => 'no-cache',
                    'Expires' => '0'
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'message' => '生成模板失败: ' . $e->getMessage(),
                'code' => 500
            ], 500);
        }
    }

    /**
     * 获取学校导入历史
     */
    public function getSchoolImportHistory(Request $request): JsonResponse
    {
        $user = Auth::user();
        $query = SchoolImportLog::query();

        // 根据用户权限过滤数据范围
        $accessScope = $user->getDataAccessScope();
        if ($accessScope['type'] === 'specific') {
            // 只显示用户自己的导入记录或其权限范围内的记录
            $query->where(function ($q) use ($user, $accessScope) {
                $q->where('user_id', $user->id)
                  ->orWhereIn('parent_id', $accessScope['organizations']);
            });
        } elseif ($accessScope['type'] === 'none') {
            $query->where('user_id', $user->id); // 只能看自己的记录
        }
        // 如果是 'all' 类型，不添加任何过滤条件

        // 搜索条件
        if ($request->filled('filename')) {
            $query->where('filename', 'like', '%' . $request->input('filename') . '%');
        }

        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->filled('parent_id')) {
            $query->where('parent_id', $request->input('parent_id'));
        }

        // 时间范围筛选
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->input('start_date'));
        }

        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->input('end_date'));
        }

        // 排序
        $query->orderBy('created_at', 'desc');

        // 分页
        $perPage = $request->input('per_page', 15);
        $logs = $query->with(['parent', 'user'])->paginate($perPage);

        return response()->json([
            'message' => '获取学校导入历史成功',
            'data' => $logs,
            'code' => 200
        ]);
    }

    /**
     * 获取学校导入详情
     */
    public function getSchoolImportDetail(SchoolImportLog $importLog): JsonResponse
    {
        $user = Auth::user();

        // 检查用户是否有权限查看该导入记录
        if (!$this->canAccessImportLog($user, $importLog)) {
            return response()->json([
                'message' => '无权查看该导入记录',
                'code' => 403
            ], 403);
        }

        $importLog->load(['parent', 'user']);

        return response()->json([
            'message' => '获取导入详情成功',
            'data' => $importLog,
            'code' => 200
        ]);
    }

    /**
     * 获取学校导入统计
     */
    public function getSchoolImportStats(Request $request, SchoolImportService $importService): JsonResponse
    {
        $user = Auth::user();
        $days = $request->input('days', 30);

        // 根据用户权限决定是否只查看自己的统计
        $userId = null;
        $accessScope = $user->getDataAccessScope();
        if ($accessScope['type'] !== 'all') {
            $userId = $user->id;
        }

        $stats = $importService->getImportStats($userId, $days);

        return response()->json([
            'message' => '获取导入统计成功',
            'data' => $stats,
            'code' => 200
        ]);
    }

    /**
     * 预览学校导入数据
     */
    public function previewSchoolImport(Request $request, SchoolImportService $importService): JsonResponse
    {
        $user = Auth::user();

        // 验证请求
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv|max:20480',
            'parent_id' => 'required|exists:organizations,id',
            'limit' => 'integer|min:1|max:50'
        ]);

        try {
            $limit = $request->input('limit', 10);
            $results = $importService->previewImport(
                $request->file('file'),
                $request->input('parent_id'),
                $user,
                $limit
            );

            return response()->json([
                'message' => '数据预览成功',
                'data' => $results,
                'code' => 200
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '数据预览失败: ' . $e->getMessage(),
                'code' => 500
            ], 500);
        }
    }

    /**
     * 分析学校导入文件
     */
    public function analyzeSchoolImportFile(Request $request, SchoolImportService $importService): JsonResponse
    {
        $user = Auth::user();

        // 验证请求
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv|max:20480',
            'parent_id' => 'required|exists:organizations,id'
        ]);

        try {
            $analysis = $importService->analyzeImportFile(
                $request->file('file'),
                $request->input('parent_id'),
                $user
            );

            return response()->json([
                'message' => '文件分析成功',
                'data' => $analysis,
                'code' => 200
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '文件分析失败: ' . $e->getMessage(),
                'code' => 500
            ], 500);
        }
    }

    /**
     * 检查用户是否可以访问导入日志
     */
    private function canAccessImportLog(User $user, SchoolImportLog $importLog): bool
    {
        $accessScope = $user->getDataAccessScope();

        if ($accessScope['type'] === 'all') {
            return true;
        }

        if ($importLog->user_id === $user->id) {
            return true;
        }

        if ($accessScope['type'] === 'specific' && $importLog->parent_id) {
            return in_array($importLog->parent_id, $accessScope['organizations']);
        }

        return false;
    }
}