PHPExcel_Style_NumberFormat::FORMAT_GENERAL = 'General'
PHPExcel_Style_NumberFormat::FORMAT_TEXT = '@'
PHPExcel_Style_NumberFormat::FORMAT_NUMBER = '0'
PHPExcel_Style_NumberFormat::FORMAT_NUMBER_00 = '0.00'
PHPExcel_Style_NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1 = '#,##0.00'
PHPExcel_Style_NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2 = '#,##0.00_-'
PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE = '0%'
PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE_00 = '0.00%'
PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD2 = 'yyyy-mm-dd'
PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD = 'yy-mm-dd'
PHPExcel_Style_NumberFormat::FORMAT_DATE_DDMMYYYY = 'dd/mm/yy'
PHPExcel_Style_NumberFormat::FORMAT_DATE_DMYSLASH = 'd/m/y'
PHPExcel_Style_NumberFormat::FORMAT_DATE_DMYMINUS = 'd-m-y'
PHPExcel_Style_NumberFormat::FORMAT_DATE_DMMINUS = 'd-m'
PHPExcel_Style_NumberFormat::FORMAT_DATE_MYMINUS = 'm-y'
PHPExcel_Style_NumberFormat::FORMAT_DATE_XLSX14 = 'mm-dd-yy'
PHPExcel_Style_NumberFormat::FORMAT_DATE_XLSX15 = 'd-mmm-yy'
PHPExcel_Style_NumberFormat::FORMAT_DATE_XLSX16 = 'd-mmm'
PHPExcel_Style_NumberFormat::FORMAT_DATE_XLSX17 = 'mmm-yy'
PHPExcel_Style_NumberFormat::FORMAT_DATE_XLSX22 = 'm/d/yy h:mm'
PHPExcel_Style_NumberFormat::FORMAT_DATE_DATETIME = 'd/m/y h:mm'
PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME1 = 'h:mm AM/PM'
PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME2 = 'h:mm:ss AM/PM'
PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME3 = 'h:mm'
PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME4 = 'h:mm:ss'
PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME5 = 'mm:ss'
PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME6 = 'h:mm:ss'
PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME7 = 'i:s.S'
PHPExcel_Style_NumberFormat::FORMAT_DATE_TIME8 = 'h:mm:ss;@'
PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDDSLASH = 'yy/mm/dd;@'
PHPExcel_Style_NumberFormat::FORMAT_CURRENCY_USD_SIMPLE = '"$"#,##0.00_-'
PHPExcel_Style_NumberFormat::FORMAT_CURRENCY_USD = '$#,##0_-'
PHPExcel_Style_NumberFormat::FORMAT_CURRENCY_EUR_SIMPLE = '[$EUR ]#,##0.00_-'

->setColumnFormat(array(
          'B' => '0',
          'D' => '0.00',
          'F' => '@',
          'F' => 'yyyy-mm-dd',
          ......
      )
  )