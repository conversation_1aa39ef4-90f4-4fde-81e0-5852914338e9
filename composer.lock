{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "bd63809bf8e1015411e61c8f03b29887", "packages": [{"name": "doctrine/inflector", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector", "Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-04-16T17:34:40+00:00"}, {"name": "illuminate/cache", "version": "v5.5.44", "source": {"type": "git", "url": "https://github.com/illuminate/cache.git", "reference": "5306a5d099395323d2e0c93beb12fa03c310d374"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/cache/zipball/5306a5d099395323d2e0c93beb12fa03c310d374", "reference": "5306a5d099395323d2e0c93beb12fa03c310d374", "shasum": ""}, "require": {"illuminate/contracts": "5.5.*", "illuminate/support": "5.5.*", "php": ">=7.0"}, "suggest": {"illuminate/database": "Required to use the database cache driver (5.5.*).", "illuminate/filesystem": "Required to use the file cache driver (5.5.*).", "illuminate/redis": "Required to use the redis cache driver (5.5.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"psr-4": {"Illuminate\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Cache package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2018-01-26T01:40:49+00:00"}, {"name": "illuminate/config", "version": "v5.5.44", "source": {"type": "git", "url": "https://github.com/illuminate/config.git", "reference": "2abd46c4948b474cb8ac08141f1c8359d93d5f2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/config/zipball/2abd46c4948b474cb8ac08141f1c8359d93d5f2e", "reference": "2abd46c4948b474cb8ac08141f1c8359d93d5f2e", "shasum": ""}, "require": {"illuminate/contracts": "5.5.*", "illuminate/support": "5.5.*", "php": ">=7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"psr-4": {"Illuminate\\Config\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Config package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-06-26T13:15:04+00:00"}, {"name": "illuminate/contracts", "version": "v5.5.44", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "b2a62b4a85485fca9cf5fa61a933ad64006ff528"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/b2a62b4a85485fca9cf5fa61a933ad64006ff528", "reference": "b2a62b4a85485fca9cf5fa61a933ad64006ff528", "shasum": ""}, "require": {"php": ">=7.0", "psr/container": "~1.0", "psr/simple-cache": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2018-03-20T15:34:35+00:00"}, {"name": "illuminate/filesystem", "version": "v5.5.44", "source": {"type": "git", "url": "https://github.com/illuminate/filesystem.git", "reference": "b8c0e36d47cfde3a0727bc6e2057775ff98a1bcd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/filesystem/zipball/b8c0e36d47cfde3a0727bc6e2057775ff98a1bcd", "reference": "b8c0e36d47cfde3a0727bc6e2057775ff98a1bcd", "shasum": ""}, "require": {"illuminate/contracts": "5.5.*", "illuminate/support": "5.5.*", "php": ">=7.0", "symfony/finder": "~3.3"}, "suggest": {"league/flysystem": "Required to use the Flysystem local and FTP drivers (~1.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (~1.0).", "league/flysystem-rackspace": "Required to use the Flysystem Rackspace driver (~1.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"psr-4": {"Illuminate\\Filesystem\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Filesystem package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2018-02-07T00:04:00+00:00"}, {"name": "illuminate/support", "version": "v5.5.44", "source": {"type": "git", "url": "https://github.com/illuminate/support.git", "reference": "5c405512d75dcaf5d37791badce02d86ed8e4bc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/support/zipball/5c405512d75dcaf5d37791badce02d86ed8e4bc4", "reference": "5c405512d75dcaf5d37791badce02d86ed8e4bc4", "shasum": ""}, "require": {"doctrine/inflector": "~1.1", "ext-mbstring": "*", "illuminate/contracts": "5.5.*", "nesbot/carbon": "^1.24.1", "php": ">=7.0"}, "replace": {"tightenco/collect": "<5.5.33"}, "suggest": {"illuminate/filesystem": "Required to use the composer class (5.5.*).", "symfony/process": "Required to use the composer class (~3.3).", "symfony/var-dumper": "Required to use the dd function (~3.3)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2018-08-10T19:40:01+00:00"}, {"name": "jeremeamia/superclosure", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/jeremeamia/super_closure.git", "reference": "5707d5821b30b9a07acfb4d76949784aaa0e9ce9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jeremeamia/super_closure/zipball/5707d5821b30b9a07acfb4d76949784aaa0e9ce9", "reference": "5707d5821b30b9a07acfb4d76949784aaa0e9ce9", "shasum": ""}, "require": {"nikic/php-parser": "^1.2|^2.0|^3.0|^4.0", "php": ">=5.4", "symfony/polyfill-php56": "^1.0"}, "require-dev": {"phpunit/phpunit": "^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"psr-4": {"SuperClosure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia", "role": "Developer"}], "description": "Serialize Closure objects, including their context and binding", "homepage": "https://github.com/jeremeamia/super_closure", "keywords": ["closure", "function", "lambda", "parser", "serializable", "serialize", "tokenizer"], "support": {"issues": "https://github.com/jeremeamia/super_closure/issues", "source": "https://github.com/jeremeamia/super_closure/tree/master"}, "abandoned": "opis/closure", "time": "2018-03-21T22:21:57+00:00"}, {"name": "kylekatarnls/update-helper", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/kylekatarnls/update-helper.git", "reference": "429be50660ed8a196e0798e5939760f168ec8ce9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kylekatarnls/update-helper/zipball/429be50660ed8a196e0798e5939760f168ec8ce9", "reference": "429be50660ed8a196e0798e5939760f168ec8ce9", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0.0", "php": ">=5.3.0"}, "require-dev": {"codeclimate/php-test-reporter": "dev-master", "composer/composer": "2.0.x-dev || ^2.0.0-dev", "phpunit/phpunit": ">=4.8.35 <6.0"}, "type": "composer-plugin", "extra": {"class": "UpdateHelper\\ComposerPlugin"}, "autoload": {"psr-0": {"UpdateHelper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Update helper", "support": {"issues": "https://github.com/kylekatarnls/update-helper/issues", "source": "https://github.com/kylekatarnls/update-helper/tree/1.2.1"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2020-04-07T20:44:10+00:00"}, {"name": "maatwebsite/excel", "version": "2.1.30", "source": {"type": "git", "url": "https://github.com/Maatwebsite/Laravel-Excel.git", "reference": "f5540c4ba3ac50cebd98b09ca42e61f926ef299f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Maatwebsite/Laravel-Excel/zipball/f5540c4ba3ac50cebd98b09ca42e61f926ef299f", "reference": "f5540c4ba3ac50cebd98b09ca42e61f926ef299f", "shasum": ""}, "require": {"illuminate/cache": "^5.0", "illuminate/config": "^5.0", "illuminate/filesystem": "^5.0", "illuminate/support": "^5.0", "jeremeamia/superclosure": "^2.3", "nesbot/carbon": "~1.0", "php": ">=5.5", "phpoffice/phpexcel": "^1.8.1", "tijsverkoyen/css-to-inline-styles": "~2.0"}, "require-dev": {"mockery/mockery": "~1.0", "orchestra/testbench": "3.1.*|3.2.*|3.3.*|3.4.*|3.5.*|3.6.*", "phpseclib/phpseclib": "~1.0", "phpunit/phpunit": "~4.0"}, "suggest": {"illuminate/http": "^5.0", "illuminate/queue": "^5.0", "illuminate/routing": "^5.0", "illuminate/view": "^5.0"}, "type": "library", "extra": {"laravel": {"aliases": {"Excel": "Maatwebsite\\Excel\\Facades\\Excel"}, "providers": ["Maatwebsite\\Excel\\ExcelServiceProvider"]}}, "autoload": {"psr-0": {"Maatwebsite\\Excel\\": "src/"}, "classmap": ["src/Maatwebsite/Excel"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Maatwebsite.nl", "email": "<EMAIL>"}], "description": "Supercharged Excel exports in Laravel", "keywords": ["PHPExcel", "batch", "csv", "excel", "export", "import", "laravel"], "support": {"issues": "https://github.com/Maatwebsite/Laravel-Excel/issues", "source": "https://github.com/Maatwebsite/Laravel-Excel/tree/2.1"}, "time": "2018-09-04T19:00:09+00:00"}, {"name": "nesbot/carbon", "version": "1.39.1", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4be0c005164249208ce1b5ca633cd57bdd42ff33", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33", "shasum": ""}, "require": {"kylekatarnls/update-helper": "^1.1", "php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"composer/composer": "^1.2", "friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "bin": ["bin/upgrade-carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "update-helper": "Carbon\\Upgrade"}, "autoload": {"psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}], "description": "A simple API extension for DateTime.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2019-10-14T05:51:36+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.4", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/715f4d25e225bc47b293a8b997fe6ce99bf987d2", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.4"}, "time": "2024-09-29T15:01:53+00:00"}, {"name": "phpoffice/phpexcel", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPExcel.git", "reference": "372c7cbb695a6f6f1e62649381aeaa37e7e70b32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPExcel/zipball/372c7cbb695a6f6f1e62649381aeaa37e7e70b32", "reference": "372c7cbb695a6f6f1e62649381aeaa37e7e70b32", "shasum": ""}, "require": {"ext-xml": "*", "ext-xmlwriter": "*", "php": ">=5.2.0"}, "type": "library", "autoload": {"psr-0": {"PHPExcel": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.maartenballiauw.be"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.rootslabs.net"}, {"name": "<PERSON>"}], "description": "PHPExcel - OpenXML - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "http://phpexcel.codeplex.com", "keywords": ["OpenXML", "excel", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PHPExcel/issues", "source": "https://github.com/PHPOffice/PHPExcel/tree/master"}, "abandoned": "phpoffice/phpspreadsheet", "time": "2015-05-01T07:00:55+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "symfony/css-selector", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/601a5ce9aaad7bf10797e3663faefce9e26c24e2", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/finder", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "b6b6ad3db3edb1b4b1c1896b1975fb684994de6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/b6b6ad3db3edb1b4b1c1896b1975fb684994de6e", "reference": "b6b6ad3db3edb1b4b1c1896b1975fb684994de6e", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-11-16T17:02:08+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.20-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php56/tree/v1.20.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T08:10:11+00:00"}, {"name": "symfony/translation", "version": "v4.4.47", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "45036b1d53accc48fe9bab71ccd86d57eba0dd94"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/45036b1d53accc48fe9bab71ccd86d57eba0dd94", "reference": "45036b1d53accc48fe9bab71ccd86d57eba0dd94", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v4.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-10-03T15:15:11+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "450d4172653f38818657022252f9d81be89ee9a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/450d4172653f38818657022252f9d81be89ee9a8", "reference": "450d4172653f38818657022252f9d81be89ee9a8", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "0d72ac1c00084279c1816675284073c5a337c20d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/0d72ac1c00084279c1816675284073c5a337c20d", "reference": "0d72ac1c00084279c1816675284073c5a337c20d", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.4 || ^8.0", "symfony/css-selector": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/v2.3.0"}, "time": "2024-12-21T16:25:41+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "plugin-api-version": "2.6.0"}