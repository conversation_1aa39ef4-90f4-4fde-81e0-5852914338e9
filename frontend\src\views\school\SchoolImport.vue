<template>
  <div class="school-import-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h2>学校信息批量导入</h2>
          <el-button type="primary" @click="downloadTemplate">下载导入模板</el-button>
        </div>
      </template>
      
      <el-form :model="importForm" ref="importFormRef" label-width="120px" :rules="rules">
        <el-form-item label="上级组织" prop="parent_id">
          <el-cascader
            v-model="importForm.parent_id"
            :options="organizationOptions"
            :props="{ checkStrictly: true, value: 'id', label: 'name', children: 'children' }"
            placeholder="请选择学校所属组织"
            clearable
            filterable
          />
        </el-form-item>
        
        <el-form-item label="Excel文件" prop="file">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            :http-request="handleUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :on-change="handleFileChange"
            :auto-upload="false"
            :file-list="fileList"
            accept=".xlsx,.xls,.csv"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              拖拽文件到此处或 <em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                请上传Excel文件(.xlsx, .xls, .csv)，文件大小不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitUpload" :loading="uploading">开始导入</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card class="box-card import-history" v-if="importLogs.length > 0">
      <template #header>
        <div class="card-header">
          <h2>导入历史记录</h2>
          <el-button type="primary" @click="refreshImportLogs">刷新</el-button>
        </div>
      </template>
      
      <el-table :data="importLogs" style="width: 100%" border>
        <el-table-column prop="file_name" label="文件名" min-width="150" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="total_rows" label="总行数" width="100" />
        <el-table-column prop="success_rows" label="成功行数" width="100" />
        <el-table-column prop="failed_rows" label="失败行数" width="100" />
        <el-table-column label="进度" width="200">
          <template #default="scope">
            <el-progress :percentage="scope.row.progress_percentage || 0" :status="getProgressStatus(scope.row.status)" />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button 
              v-if="scope.row.status === 'pending' || scope.row.status === 'processing'"
              type="primary" 
              size="small" 
              @click="checkStatus(scope.row.id)"
            >
              查看进度
            </el-button>
            <el-button 
              v-if="scope.row.status === 'failed'"
              type="danger" 
              size="small" 
              @click="viewErrors(scope.row)"
            >
              查看错误
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 错误详情对话框 -->
    <el-dialog
      v-model="errorDialogVisible"
      title="导入错误详情"
      width="70%"
    >
      <el-table :data="errorDetails" style="width: 100%" border>
        <el-table-column prop="row" label="行号" width="80" />
        <el-table-column label="数据" min-width="200">
          <template #default="scope">
            <div v-for="(value, key) in scope.row.data" :key="key" class="error-data-item">
              <strong>{{ key }}:</strong> {{ value }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="错误信息" min-width="200">
          <template #default="scope">
            <div v-if="scope.row.message">{{ scope.row.message }}</div>
            <div v-else-if="scope.row.errors">
              <div v-for="(errors, field) in scope.row.errors" :key="field" class="error-message">
                <strong>{{ field }}:</strong> {{ errors.join(', ') }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="errorDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import axios from 'axios';

// 表单数据
const importForm = reactive({
  parent_id: null,
  file: null
});

// 表单验证规则
const rules = {
  parent_id: [
    { required: true, message: '请选择上级组织', trigger: 'change' }
  ],
  file: [
    { required: true, message: '请选择要上传的文件', trigger: 'change' }
  ]
};

// 组织机构选项
const organizationOptions = ref([]);
// 文件列表
const fileList = ref([]);
// 导入日志
const importLogs = ref([]);
// 上传中状态
const uploading = ref(false);
// 表单引用
const importFormRef = ref(null);
// 错误对话框
const errorDialogVisible = ref(false);
// 错误详情
const errorDetails = ref([]);

// 获取组织机构树
const getOrganizationTree = async () => {
  try {
    const response = await axios.get('/api/organizations/tree');
    organizationOptions.value = response.data.data;
  } catch (error) {
    console.error('获取组织机构树失败:', error);
    ElMessage.error('获取组织机构树失败');
  }
};

// 获取导入历史记录
const getImportLogs = async () => {
  try {
    const response = await axios.get('/api/school-import');
    importLogs.value = response.data.data.import_logs;
  } catch (error) {
    console.error('获取导入历史记录失败:', error);
    ElMessage.error('获取导入历史记录失败');
  }
};

// 刷新导入历史记录
const refreshImportLogs = () => {
  getImportLogs();
};

// 下载导入模板
const downloadTemplate = async () => {
  try {
    const response = await axios.get('/api/school-import/template');
    ElMessage.success(response.data.message || '模板下载功能尚未实现');
    // 如果后端实现了模板下载功能，这里需要处理文件下载
  } catch (error) {
    console.error('下载模板失败:', error);
    ElMessage.error('下载模板失败');
  }
};

// 处理文件变更
const handleFileChange = (file) => {
  importForm.file = file.raw;
};

// 处理超出文件数量限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件');
};

// 自定义上传处理
const handleUpload = async ({ file }) => {
  // 这个函数不会被调用，因为我们设置了auto-upload为false
  // 实际上传逻辑在submitUpload中处理
};

// 提交上传
const submitUpload = async () => {
  if (!importFormRef.value) return;
  
  await importFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error('请完善表单信息');
      return;
    }
    
    if (!importForm.file) {
      ElMessage.error('请选择要上传的文件');
      return;
    }
    
    uploading.value = true;
    
    try {
      const formData = new FormData();
      formData.append('file', importForm.file);
      formData.append('parent_id', importForm.parent_id[importForm.parent_id.length - 1]);
      
      const response = await axios.post('/api/school-import/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      ElMessage.success(response.data.message || '文件上传成功，正在处理导入');
      
      // 重置表单
      resetForm();
      
      // 刷新导入历史记录
      getImportLogs();
      
      // 如果有导入ID，开始轮询状态
      if (response.data.data && response.data.data.import_log_id) {
        pollImportStatus(response.data.data.import_log_id);
      }
    } catch (error) {
      console.error('上传失败:', error);
      ElMessage.error(error.response?.data?.message || '上传失败');
    } finally {
      uploading.value = false;
    }
  });
};

// 轮询导入状态
const pollImportStatus = async (importId) => {
  try {
    const response = await axios.get(`/api/school-import/status/${importId}`);
    const importLog = response.data.data.import_log;
    
    // 更新导入日志列表中的对应记录
    const index = importLogs.value.findIndex(log => log.id === importId);
    if (index !== -1) {
      importLogs.value[index] = importLog;
    } else {
      importLogs.value.unshift(importLog);
    }
    
    // 如果导入仍在进行中，继续轮询
    if (importLog.status === 'pending' || importLog.status === 'processing') {
      setTimeout(() => pollImportStatus(importId), 2000);
    } else {
      // 导入完成或失败，显示结果
      if (importLog.status === 'completed') {
        ElMessage.success(`导入完成: 成功${importLog.success_rows}条，失败${importLog.failed_rows}条`);
      } else if (importLog.status === 'failed') {
        ElMessage.error(`导入失败: ${importLog.error_details?.message || '未知错误'}`);
      }
    }
  } catch (error) {
    console.error('获取导入状态失败:', error);
    ElMessage.error('获取导入状态失败');
  }
};

// 检查导入状态
const checkStatus = (importId) => {
  pollImportStatus(importId);
};

// 查看错误详情
const viewErrors = (importLog) => {
  errorDetails.value = importLog.error_details?.errors || [];
  errorDialogVisible.value = true;
};

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'pending': return 'info';
    case 'processing': return 'warning';
    case 'completed': return 'success';
    case 'failed': return 'danger';
    default: return 'info';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'pending': return '等待处理';
    case 'processing': return '处理中';
    case 'completed': return '已完成';
    case 'failed': return '失败';
    default: return status;
  }
};

// 获取进度条状态
const getProgressStatus = (status) => {
  switch (status) {
    case 'pending': return '';
    case 'processing': return '';
    case 'completed': return 'success';
    case 'failed': return 'exception';
    default: return '';
  }
};

// 重置表单
const resetForm = () => {
  if (importFormRef.value) {
    importFormRef.value.resetFields();
  }
  fileList.value = [];
  importForm.file = null;
};

// 组件挂载时
onMounted(() => {
  getOrganizationTree();
  getImportLogs();
});
</script>

<style scoped>
.school-import-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.import-history {
  margin-top: 30px;
}

.error-data-item, .error-message {
  margin-bottom: 5px;
}

.el-upload {
  width: 100%;
}
</style>
