<template>
  <div class="district-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>学区划分管理</span>
          <el-tag type="warning" size="small">🚧 开发中</el-tag>
        </div>
      </template>

      <div class="development-notice">
        <el-result
          icon="warning"
          title="功能开发中"
          sub-title="学区划分管理功能正在开发中，敬请期待..."
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/organizations')">返回组织列表</el-button>
          </template>
        </el-result>
      </div>

      <!-- 功能预览 -->
      <div class="feature-preview">
        <h3>功能预览</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="hover" class="preview-card">
              <template #header>
                <div class="preview-header">
                  <el-icon><MapLocation /></el-icon>
                  <span>地理位置划分</span>
                </div>
              </template>
              <p>基于学校地理位置自动划分学区，支持地图可视化操作。</p>
              <div class="preview-features">
                <el-tag size="small">地图展示</el-tag>
                <el-tag size="small">自动划分</el-tag>
                <el-tag size="small">边界调整</el-tag>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card shadow="hover" class="preview-card">
              <template #header>
                <div class="preview-header">
                  <el-icon><DataAnalysis /></el-icon>
                  <span>规模因素分析</span>
                </div>
              </template>
              <p>考虑学校规模、学生数量等因素进行智能学区划分。</p>
              <div class="preview-features">
                <el-tag size="small">规模分析</el-tag>
                <el-tag size="small">负载均衡</el-tag>
                <el-tag size="small">智能推荐</el-tag>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card shadow="hover" class="preview-card">
              <template #header>
                <div class="preview-header">
                  <el-icon><Edit /></el-icon>
                  <span>手动调整</span>
                </div>
              </template>
              <p>支持管理员手动调整学区划分，灵活应对特殊情况。</p>
              <div class="preview-features">
                <el-tag size="small">拖拽操作</el-tag>
                <el-tag size="small">批量调整</el-tag>
                <el-tag size="small">历史记录</el-tag>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 开发计划 -->
      <div class="development-plan">
        <h3>开发计划</h3>
        <el-timeline>
          <el-timeline-item
            timestamp="第一阶段"
            type="primary"
            size="large"
          >
            <el-card>
              <h4>基础数据管理</h4>
              <p>实现学校地理信息录入、学区基础数据管理功能。</p>
              <el-progress :percentage="0" status="exception" />
            </el-card>
          </el-timeline-item>

          <el-timeline-item
            timestamp="第二阶段"
            type="warning"
            size="large"
          >
            <el-card>
              <h4>自动划分算法</h4>
              <p>开发基于地理位置和学校规模的自动划分算法。</p>
              <el-progress :percentage="0" status="exception" />
            </el-card>
          </el-timeline-item>

          <el-timeline-item
            timestamp="第三阶段"
            type="success"
            size="large"
          >
            <el-card>
              <h4>可视化界面</h4>
              <p>实现地图可视化展示和交互式学区调整功能。</p>
              <el-progress :percentage="0" status="exception" />
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 这里将来会添加学区划分的逻辑
</script>

<style scoped>
.district-management {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.development-notice {
  text-align: center;
  padding: 40px 0;
}

.feature-preview {
  margin-top: 40px;
  padding: 20px 0;
}

.feature-preview h3 {
  margin-bottom: 20px;
  color: #303133;
  text-align: center;
}

.preview-card {
  height: 200px;
  margin-bottom: 20px;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-features {
  margin-top: 15px;
}

.preview-features .el-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}

.development-plan {
  margin-top: 40px;
  padding: 20px 0;
}

.development-plan h3 {
  margin-bottom: 20px;
  color: #303133;
  text-align: center;
}

.development-plan .el-card {
  margin-top: 10px;
}

.development-plan h4 {
  margin-bottom: 10px;
  color: #409EFF;
}

.development-plan p {
  margin-bottom: 15px;
  color: #606266;
}
</style>
