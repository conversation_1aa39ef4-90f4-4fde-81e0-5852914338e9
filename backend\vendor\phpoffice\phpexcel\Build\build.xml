<?xml version="1.0"?>
<project name="PHPExcel" default="release-standard" basedir=".">
    <target name="gather">
        <if>
			<isset property="${packageVersion}"/>
			<then>
				<echo message="PHPExcel version number is ${packageVersion}" />
			</then>
			<else>
				<propertyprompt propertyName="packageVersion" defaultValue="1.0.0"
								promptText="Enter PHPExcel version number"/>
			</else>
        </if>

        <if>
			<isset property="${releaseDate}"/>
			<then>
				<echo message="Release date is ${releaseDate}" />
			</then>
			<else>
				<propertyprompt propertyName="releaseDate" defaultValue="2010-01-01"
								promptText="Enter PHPExcel release date"/>
			</else>
        </if>

        <if>
			<isset property="${documentFormat}"/>
			<then>
				<echo message="Document Format is ${documentFormat}" />
			</then>
			<else>
				<propertyprompt propertyName="documentFormat" defaultValue="doc"
								promptText="Enter Document Format"/>
			</else>
        </if>
    </target>

    <target name="prepare" depends="gather">
        <echo msg="Creating build directory: ./build"/>
        <mkdir dir="${phing.dir}/build"/>
    </target>

    <target name="build" depends="prepare">
        <echo msg="Copying source files to build directory..."/>

        <copy todir="${phing.dir}/build/Classes" overwrite="true">
            <fileset dir="${phing.dir}/../Classes">
                <include name="**/*"/>
            </fileset>
        </copy>

        <copy todir="${phing.dir}/build/Examples" overwrite="true">
            <fileset dir="${phing.dir}/../Examples">
                <include name="**/*"/>
            </fileset>
        </copy>

        <copy todir="${phing.dir}/build/Documentation" overwrite="true">
            <fileset dir="${phing.dir}/../Documentation">
                <include name="*.${documentFormat}"/>
                <include name="*.txt"/>
            </fileset>
        </copy>
        <mkdir dir="${phing.dir}/build/Documentation/API"/>

        <copy todir="${phing.dir}/build/Documentation/Examples" overwrite="true">
            <fileset dir="${phing.dir}/../Documentation/Examples">
                <include name="**/*"/>
                <exclude name="assets"/>
            </fileset>
        </copy>

        <copy file="${phing.dir}/../changelog.txt" tofile="${phing.dir}/build/changelog.txt" overwrite="true"/>
        <copy file="${phing.dir}/../license.md" tofile="${phing.dir}/build/license.txt" overwrite="true"/>
        <copy file="${phing.dir}/../install.txt" tofile="${phing.dir}/build/install.txt" overwrite="true"/>
    </target>

    <target name="versionNumber" depends="build">
        <reflexive>
            <fileset dir="${phing.dir}/build">
                <include pattern="**/*"/>
            </fileset>
            <filterchain>
                <replaceregexp>
                    <regexp pattern="##VERSION##" replace="${packageVersion}"/>
                    <regexp pattern="##DATE##" replace="${releaseDate}"/>
                </replaceregexp>
            </filterchain>
        </reflexive>

        <reflexive>
            <fileset dir="${phing.dir}/build">
                <include pattern="**/changelog.txt"/>
            </fileset>
            <filterchain>
                <replaceregexp>
                    <regexp pattern="Fixed in develop branch for release v${packageVersion}" replace="${releaseDate} (v${packageVersion})"/>
                </replaceregexp>
            </filterchain>
        </reflexive>
    </target>

    <target name="apidocs" depends="versionNumber">
        <echo msg="Generating API documentation..."/>
        <phpdoc2 title="PHPExcel classes"
                 destdir="${phing.dir}/build/Documentation/API"
				 template="responsive">
            <fileset dir="${phing.dir}/build/Classes">
                <include name="**/*.php"/>
            </fileset>
        </phpdoc2>
    </target>

    <target name="release-standard" depends="apidocs">
        <mkdir dir="${phing.dir}/release"/>

        <echo msg="Creating release package (v${packageVersion} with ${documentFormat} documents)..."/>
        <zip destfile="${phing.dir}/release/PHPExcel_${packageVersion}_${documentFormat}.zip">
            <fileset dir="${phing.dir}/build">
                <include name="**/*"/>
            </fileset>
        </zip>

        <echo msg="Cleaning build directory: ./build"/>
        <delete dir="${phing.dir}/build"/>
    </target>

    <target name="release-phar" depends="versionNumber">
        <mkdir dir="${phing.dir}/release"/>

        <echo msg="Creating PHAR release package (v${packageVersion})..."/>
        <pharpackage destfile="${phing.dir}/release/PHPExcel_${packageVersion}.phar" basedir="${phing.dir}/build/Classes" compression="gzip" stub="${phing.dir}/PharStub.php">
            <fileset dir="${phing.dir}/build/Classes">
                <include name="**/**" />
            </fileset>
            <metadata>
                <element name="version" value="${packageVersion}" />
                <element name="date" value="${releaseDate}" />
                <element name="description" value="A pure PHP library for reading and writing spreadsheet files" />
                <element name="authors">
                    <element name="Mark Baker">
                        <element name="e-mail" value="<EMAIL>" />
                    </element>
                </element>
            </metadata>
        </pharpackage>

        <echo msg="Cleaning build directory: ./build"/>
        <delete dir="${phing.dir}/build"/>
    </target>

    <target name="release-pear" depends="versionNumber">
        <taskdef classname="phing.tasks.ext.d51PearPkg2Task" name="d51pearpkg2"/>

        <mkdir dir="${phing.dir}/release"/>

        <echo msg="Creating PEAR release package (v${packageVersion})..."/>

        <d51pearpkg2 dir="${phing.dir}/build/Classes" baseinstalldir="PHPExcel">
            <name>PHPExcel</name>
            <summary>PHP Excel classes</summary>
            <channel>pear.pearplex.net</channel>
            <description>Project providing a set of classes for the PHP programming language, which allow you to write
                to Excel 2007 files and read from Excel 2007 files.
            </description>
            <notes>This package ONLY contains the class files, not the documentation and example code. Please refer to
                http://www.codeplex.com/PHPExcel for those files.
            </notes>
            <lead user="maartenba" name="Maarten Balliauw" email="<EMAIL>"/>
            <license uri="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.txt">LGPL</license>
            <version release="${packageVersion}" api="${packageVersion}"/>
            <stability release="stable" api="stable"/>
            <dependencies>
                <php minimum_version="5.2.0"/>
                <pear minimum_version="1.4.0"/>
                <extension name="zip" minimum_version="1.8.0"/>
            </dependencies>
            <dirroles key="PHPExcel/Shared/PDF/fonts">data</dirroles>
            <dirroles key="PHPExcel/Shared/PDF/fonts/utils">data</dirroles>
            <dirroles key="/PHPExcel/Shared/PDF/fonts/utils">data</dirroles>
        </d51pearpkg2>

        <exec command="pear package ${phing.dir}/build/Classes/package.xml"/>
        <move file="PHPExcel-${packageVersion}.tgz" tofile="${phing.dir}/release/PHPExcel-${packageVersion}.tgz" overwrite="true"/>

        <echo msg="Cleaning build directory: ./build"/>
        <delete dir="${phing.dir}/build"/>
    </target>

    <target name="release-documentation" depends="gather">
        <mkdir dir="${phing.dir}/release"/>

        <echo msg="Creating documentation release (v${packageVersion} with ${documentFormat} documents)..."/>
        <copy todir="${phing.dir}/build" overwrite="true">
            <fileset dir="${phing.dir}/../Documentation">
                <include name="*.${documentFormat}"/>
                <include name="*.txt"/>
            </fileset>
        </copy>
        <copy todir="${phing.dir}/build/Examples" overwrite="true">
            <fileset dir="${phing.dir}/../Documentation/Examples">
                <include name="**/*"/>
            </fileset>
        </copy>

        <echo msg="Creating documentation release package (v${packageVersion} with ${documentFormat} documents)..."/>
        <zip destfile="${phing.dir}/release/PHPExcel_${packageVersion}-documentation_${documentFormat}.zip">
            <fileset dir="${phing.dir}/build">
                <include name="**/*"/>
            </fileset>
        </zip>

        <echo msg="Cleaning build directory: ./build"/>
        <delete dir="${phing.dir}/build"/>
    </target>
</project>
